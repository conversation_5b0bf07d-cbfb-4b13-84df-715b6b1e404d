// Import the dotenv package to load environment variables from .env file
import 'dotenv/config';

// Import the existing app.json configuration
import appJson from './app.json';

// Get environment variables
const oktaDomain = process.env.OKTA_DOMAIN;
const oktaClientId = process.env.OKTA_CLIENT_ID;
const oktaRedirectUri = process.env.OKTA_REDIRECT_URI;
const appEnv = process.env.APP_ENV || 'development';
const dcApiBaseUrl = process.env.DC_API_BASE_URL;
const dcApiKey = process.env.DC_API_KEY;

// Add environment variables to the app configuration
export default {
  ...appJson,
  expo: {
    ...appJson.expo,
    extra: {
      // Preserve existing extra properties from app.json
      ...appJson.expo.extra,

      // Okta configuration
      oktaDomain,
      oktaClientId,
      oktaRedirectUri,

      // Environment
      appEnv,

      // Digital Core API configuration
      dcApiBaseUrl,
      dcApi<PERSON><PERSON>,

    },
  },
};
