# Environment
# Options: development, test, acceptance, production
APP_ENV=development

# Okta Configuration (EXPO_PUBLIC_ prefix makes these available in client)
EXPO_PUBLIC_OKTA_DOMAIN=login.dev.example.com
EXPO_PUBLIC_OKTA_CLIENT_ID=devclientid123
EXPO_PUBLIC_OKTA_REDIRECT_URI=com.example.dev:/callback
EXPO_PUBLIC_OKTA_LOGOUT_REDIRECT_URI=com.example.dev:/logout

# Digital Core API Configuration (EXPO_PUBLIC_ prefix makes these available in client)
EXPO_PUBLIC_DC_API_BASE_URL=https://api.dev.example.com/v1
EXPO_PUBLIC_DC_API_KEY_PUBLIC=publickey123
EXPO_PUBLIC_DC_API_KEY_PRIVATE=privatekey456

# Build-time variables (for app.config.js)
OKTA_DOMAIN=login.dev.example.com
OKTA_CLIENT_ID=devclientid123
OKTA_REDIRECT_URI=com.example.dev:/callback
OKTA_LOGOUT_REDIRECT_URI=com.example.dev:/logout
DC_API_BASE_URL=https://api.dev.example.com/v1
DC_API_KEY_PUBLIC=publickey789
DC_API_KEY_PRIVATE=privatekey012
