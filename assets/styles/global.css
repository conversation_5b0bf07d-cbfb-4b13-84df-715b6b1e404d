/* Global styles for web platform */

html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-y: auto;
  font-family: 'Etelka', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background:'red' !important;
} 

/* Theme-specific styles */
html[data-theme="light"] {
  background-color: #FFFFFF;
  color: #111827;
}

html[data-theme="dark"] {
  background-color: #111827;
  color: #F9FAFB;
}

/* Ensure scrolling works properly */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

html[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}
