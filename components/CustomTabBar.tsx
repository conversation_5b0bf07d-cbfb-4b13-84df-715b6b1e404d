import { tokens } from '@/src/design-system';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { HapticTab } from './HapticTab';

/**
 * Custom tab bar component with a half-circle indicator for the selected tab
 */
export function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  return (
    <View style={styles.container}>
      {/* Half-circle indicator for selected tab */}
      <View
        style={[
          styles.indicator,
          {
            left: (Dimensions.get('window').width / state.routes.length) * state.index +
                 (Dimensions.get('window').width / state.routes.length / 2) - 15,
          }
        ]}
      />

      {/* Tab bar items */}
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label = options.title || route.name;
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          // Get the active and inactive colors
          const activeColor = options.tabBarActiveTintColor || tokens.colors.secondaryColors.accentGreen700;
          const inactiveColor = options.tabBarInactiveTintColor || tokens.colors.neutralColors.neutral800;
          const color = isFocused ? activeColor : inactiveColor;

          return (
            <HapticTab
              key={index}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              // Use optional chaining for testID
              testID={(options as any).tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tab}
            >
              <View style={styles.tabContent}>
                {/* Render the icon */}
                {options.tabBarIcon &&
                  options.tabBarIcon({
                    focused: isFocused,
                    color,
                    size: 24
                  })
                }

                {/* Render the label */}
                {options.tabBarLabel ? (
                  typeof options.tabBarLabel === 'function' ? (
                    options.tabBarLabel({
                      focused: isFocused,
                      color,
                      position: 'below-icon',
                      children: label
                    })
                  ) : (
                    <Text
                      style={{
                        color,
                        fontSize: 12,
                        fontFamily: tokens.typography.fontFamilies.base,
                        fontWeight: isFocused ? '500' : '300',
                        marginTop: 4,
                        textAlign: 'center'
                      }}
                    >
                      {options.tabBarLabel}
                    </Text>
                  )
                ) : (
                  <Text
                    style={{
                      color,
                      fontSize: 12,
                      fontFamily: tokens.typography.fontFamilies.base,
                      fontWeight: isFocused ? '500' : '300',
                      marginTop: 4,
                      textAlign: 'center'
                    }}
                  >
                    {label}
                  </Text>
                )}
              </View>
            </HapticTab>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  indicator: {
    position: 'absolute',
    top: -10,
    width: 30,
    height: 15,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: tokens.colors.secondaryColors.accentGreen700,
    zIndex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    borderTopWidth: 1,
    borderTopColor: tokens.colors.borderColors.borderDividerLowEmphasis,
    paddingBottom: 5,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
