/**
 * Hook for using Bluetooth audio functionality
 */

import { useCallback, useEffect, useState } from 'react';
import { audioService } from '../services/AudioService';
import { BluetoothDevice, bluetoothService } from '../services/BluetoothService';

// Define the error types
export type BluetoothErrorType =
  | 'BLUETOOTH_NOT_ENABLED'
  | 'BLUETOOTH_PERMISSION_DENIED'
  | 'SCAN_ERROR'
  | 'CONNECTION_ERROR'
  | 'AUDIO_LOAD_ERROR'
  | 'AUDIO_PLAY_ERROR'
  | 'UNKNOWN_ERROR';

// Define the hook return type
interface UseBluetoothAudioReturn {
  isBluetoothEnabled: boolean;
  isScanning: boolean;
  isConnecting: boolean;
  isPlaying: boolean;
  devices: BluetoothDevice[];
  connectedDevice: BluetoothDevice | null;
  error: { type: BluetoothErrorType; message: string } | null;
  startScan: () => Promise<void>;
  stopScan: () => void;
  connectToDevice: (deviceId: string) => Promise<void>;
  disconnectFromDevice: (deviceId: string) => Promise<void>;
  playAudio: () => Promise<void>;
  pauseAudio: () => Promise<void>;
  resetError: () => void;
}

/**
 * Hook for using Bluetooth audio functionality
 */
export const useBluetoothAudio = (): UseBluetoothAudioReturn => {
  // State
  const [isBluetoothEnabled, setIsBluetoothEnabled] = useState<boolean>(false);
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [devices, setDevices] = useState<BluetoothDevice[]>([]);
  const [connectedDevice, setConnectedDevice] = useState<BluetoothDevice | null>(null);
  const [error, setError] = useState<{ type: BluetoothErrorType; message: string } | null>(null);

  // Check Bluetooth status on mount
  useEffect(() => {
    const checkBluetoothStatus = async () => {
      try {
        const enabled = await bluetoothService.isBluetoothEnabled();
        setIsBluetoothEnabled(enabled);
      } catch (err) {
        setError({
          type: 'UNKNOWN_ERROR',
          message: 'Failed to check Bluetooth status',
        });
      }
    };

    checkBluetoothStatus();

    // Clean up on unmount
    return () => {
      bluetoothService.stopScan();
    };
  }, []);

  // Start scanning for devices
  const startScan = useCallback(async () => {
    try {
      // Reset error
      setError(null);

      // Check if Bluetooth is enabled
      const enabled = await bluetoothService.isBluetoothEnabled();
      if (!enabled) {
        setError({
          type: 'BLUETOOTH_NOT_ENABLED',
          message: 'Bluetooth is not enabled',
        });
        return;
      }

      // Request permissions
      const permissionsGranted = await bluetoothService.requestPermissions();
      if (!permissionsGranted) {
        setError({
          type: 'BLUETOOTH_PERMISSION_DENIED',
          message: 'Bluetooth permissions denied',
        });
        return;
      }

      // Start scanning
      setIsScanning(true);
      setDevices([]);

      await bluetoothService.startScan((device) => {
        setDevices((prevDevices) => {
          // Check if device already exists
          const exists = prevDevices.some((d) => d.id === device.id);
          if (exists) {
            return prevDevices;
          }
          return [...prevDevices, device];
        });
      });
    } catch (err) {
      setError({
        type: 'SCAN_ERROR',
        message: 'Failed to scan for devices',
      });
    }
  }, []);

  // Stop scanning for devices
  const stopScan = useCallback(() => {
    bluetoothService.stopScan();
    setIsScanning(false);
  }, []);

  // Connect to a device
  const connectToDevice = useCallback(async (deviceId: string) => {
    try {
      setIsConnecting(true);
      setError(null);

      const device = await bluetoothService.connectToDevice(deviceId);
      setConnectedDevice(device);

      // For demo purposes, we'll use a sample audio URL
      // In a real app, you would bundle the audio file with the app
      const audioUri = 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3';
      await audioService.loadAudio(audioUri);
    } catch (err) {
      setError({
        type: 'CONNECTION_ERROR',
        message: 'Failed to connect to device',
      });
    } finally {
      setIsConnecting(false);
    }
  }, []);

  // Disconnect from a device
  const disconnectFromDevice = useCallback(async (deviceId: string) => {
    try {
      await bluetoothService.disconnectFromDevice(deviceId);
      setConnectedDevice(null);

      // Unload audio
      await audioService.unloadAudio();
    } catch (err) {
      setError({
        type: 'CONNECTION_ERROR',
        message: 'Failed to disconnect from device',
      });
    }
  }, []);

  // Play audio
  const playAudio = useCallback(async () => {
    try {
      setError(null);
      await audioService.playAudio();
      setIsPlaying(true);
    } catch (err) {
      setError({
        type: 'AUDIO_PLAY_ERROR',
        message: 'Failed to play audio',
      });
    }
  }, []);

  // Pause audio
  const pauseAudio = useCallback(async () => {
    try {
      await audioService.pauseAudio();
      setIsPlaying(false);
    } catch (err) {
      setError({
        type: 'AUDIO_PLAY_ERROR',
        message: 'Failed to pause audio',
      });
    }
  }, []);

  // Reset error
  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isBluetoothEnabled,
    isScanning,
    isConnecting,
    isPlaying,
    devices,
    connectedDevice,
    error,
    startScan,
    stopScan,
    connectToDevice,
    disconnectFromDevice,
    playAudio,
    pauseAudio,
    resetError,
  };
};
