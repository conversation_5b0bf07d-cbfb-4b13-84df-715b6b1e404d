/**
 * API Types for Digital Core (DC) API
 */

// Profile API Response
export interface ProfileResponseData {
  customerId: number;
  userAccount: {
    userName: string;
    lastLogin: string;
    primaryEmail?: string;
  };
  customerType: string;
  features: string[];
  contact: {
    name: string;
    firstName?: string;
    gender?: string;
    emailAddress: string;
    preferences: null;
    phoneNumber: string;
    mobilePhoneNumber: string;
    address: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
  };
  person: null | {
    firstName: null | string;
    gender: string;
    initials: string;
    name: string;
    salutation: string;
    surname: string;
    surnamePreposition: null | string;
    dateOfBirth: null | string;
  };
  organisation: null | {
    name: string;
    kvk: string;
  };
  accounts: {
    accountId: number;
    address: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
    correspondenceAddress: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
    active: boolean;
    customerProfileType: string;
    productTypes: string[];
    productTypeDetails: {
      productType: string;
      isDynamicPricing: boolean;
      priceDifferentiation: string;
    }[];
    nextChargeDate: null | string;
    startDate: string;
    endDate: null | string;
    hasRedelivery: boolean;
    hasRedeliveryCostTariff: boolean;
    hasServiceContract: boolean;
    meterDetails: {
      productType: string;
      isSmartMeterReadingAllowed: boolean;
      isSmartMeter: boolean;
    }[];
    classification: null | string;
    hasDynamicPricing: boolean;
  }[];
  orders: null | any;
  outstandingReadings: any[];
  readingInfo: null | any;
  relocationInfo: {
    hasRelocation: boolean;
    moveOutDateSpecified: null | string;
  };
  hasOpenContractRenewal?: boolean;
}

// Wrapper response structure
export interface ProfileResponse {
  data: ProfileResponseData;
}

// Meters Status API Response
export interface MetersStatusResponse {
  meters: {
    utilityType: string;
    gridOperator: string;
    inError: boolean;
    errors: any[];
  }[];
}

// Usages API Response
export interface UsagesResponse {
  // Define the structure based on the provided JSON
  // This is a placeholder and should be expanded based on the actual response
  [key: string]: any;
}

// Common API Error Response
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}
