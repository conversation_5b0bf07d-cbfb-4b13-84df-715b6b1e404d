/**
 * Hook for using the Digital Core API
 */

import { useAuth } from '@/src/auth/AuthContext';
import { useCallback, useState } from 'react';
import { digitalCoreService } from '../DigitalCoreService';
import { MetersStatusResponse, ProfileResponse, UsagesResponse } from '../types';

/**
 * Hook for using the Digital Core API
 */
export const useDigitalCore = () => {
  const { authState } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Extract customer ID from the user profile
  // For now, hardcode the customer ID that works with the curl command
  const customerId = 14911126;
  console.log("Using hardcoded customer ID:", customerId);

  /**
   * Get the customer profile
   */
  const getProfile = useCallback(async (): Promise<ProfileResponse | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const profile = await digitalCoreService.getProfile(customerId);
      console.log("Profile data structure:", JSON.stringify(profile, null, 2));
      return profile;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch profile');
      setError(error);
      console.error('Error fetching profile:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  /**
   * Get the meters status
   * @param accountId The account ID
   */
  const getMetersStatus = useCallback(async (accountId: number): Promise<MetersStatusResponse | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const metersStatus = await digitalCoreService.getMetersStatus(customerId, accountId);
      return metersStatus;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch meters status');
      setError(error);
      console.error('Error fetching meters status:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  /**
   * Get the energy usages
   * @param accountId The account ID
   * @param options The options for the request
   */
  const getUsages = useCallback(async (
    accountId: number,
    options: {
      aggregation: 'Month' | 'Day' | 'Hour';
      interval: 'Month' | 'Day' | 'Hour';
      start: string; // ISO date string
      end: string; // ISO date string
      addBudget?: boolean;
      addWeather?: boolean;
      extrapolate?: boolean;
    }
  ): Promise<UsagesResponse | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const usages = await digitalCoreService.getUsages(customerId, accountId, options);
      return usages;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch usages');
      setError(error);
      console.error('Error fetching usages:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  return {
    isLoading,
    error,
    customerId,
    getProfile,
    getMetersStatus,
    getUsages,
  };
};
