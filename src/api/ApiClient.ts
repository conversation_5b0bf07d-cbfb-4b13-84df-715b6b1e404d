/**
 * Base API Client for Digital Core (DC) API
 */

import { Platform } from 'react-native';
import * as OktaService from '../auth/OktaService';
import env from '../config/env';
import { ApiErrorResponse } from './types';

// Define the request options type
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
}

/**
 * Base API Client for Digital Core (DC) API
 */
export class ApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = env.dcApiBaseUrl;

    // Use the public API key from the environment
    this.apiKey = env.dcApiKey; // Public API key

    console.log("API Client initialized with base URL:", this.baseUrl);
    console.log("Using API key:", this.apiKey);
  }

  /**
   * Get the authorization header with the access token
   * @returns The authorization header or null if no token is available
   */
  private async getAuthorizationHeader(): Promise<string | null> {
    try {
      const tokens = await OktaService.getTokens();
      if (tokens?.accessToken) {
        console.log("Access token found, using it for authorization");
        return `Bearer ${tokens.accessToken}`;
      }
      console.log("No access token found");

      // For testing, return a hardcoded token that matches the format expected by the API
      // This is a placeholder and should be replaced with a real token in production
      return null;
    } catch (error) {
      console.error('Error getting authorization header:', error);
      return null;
    }
  }

  /**
   * Get the common headers for all requests
   * @param authHeader The authorization header
   * @returns The common headers
   */
  private getCommonHeaders(authHeader: string | null): Record<string, string> {
    const headers: Record<string, string> = {
      'accept': 'application/json',
      'accept-language': 'nl-NL',
      'apikey': this.apiKey,
    };

    // Add authorization header if available
    if (authHeader) {
      headers['authorization'] = authHeader;
      console.log("Authorization header added");
    } else {
      console.log("No authorization header available");
    }

    // Add user-agent header for web platform
    if (Platform.OS === 'web') {
      headers['user-agent'] = navigator.userAgent;
    }

    console.log("Common headers:", JSON.stringify(headers, null, 2));
    return headers;
  }
  /**
   * Make a request to the API
   * @param endpoint The API endpoint
   * @param options The request options
   * @returns The response data
   * @throws ApiErrorResponse if the request fails
   */
  public async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    try {
      const authHeader = await this.getAuthorizationHeader();
      const headers = {
        ...this.getCommonHeaders(authHeader),
        ...options.headers,
      };

      // Prepare request options
      const requestOptions: RequestInit = {
        method: options.method || 'GET',
        headers,
      };

      // Add body if provided
      if (options.body) {
        requestOptions.body = JSON.stringify(options.body);
        headers['content-type'] = 'application/json';
      }

      // Make the request
      const url = `${this.baseUrl}${endpoint}`;
      console.log(`Making ${requestOptions.method} request to ${url}`);
      console.log('Request headers:', headers);

      const response = await fetch(url, requestOptions);
      console.log(`Response from ${url}:`, response);
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));

      // Check if the response is ok
      if (!response.ok) {
        console.error(`API error with status ${response.status}`);
        throw {
          error: 'API Error',
          message: `Request failed with status ${response.status}`,
          statusCode: response.status,
        } as ApiErrorResponse;
      }

      // Parse and return the response
      try {
        const data = await response.json();
        console.log('Response data:', data);
        return data as T;
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw {
          error: 'Parse Error',
          message: 'Failed to parse the API response',
          statusCode: 0,
        } as ApiErrorResponse;
      }
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();
