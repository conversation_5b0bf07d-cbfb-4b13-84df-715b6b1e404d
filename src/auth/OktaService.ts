import * as AuthSession from "expo-auth-session";
import * as SecureS<PERSON> from "expo-secure-store";
import * as <PERSON><PERSON>rowser from "expo-web-browser";
import { useEffect } from "react";
import { Platform } from "react-native";
import env from "../config/env";

// Define the shape of our auth tokens
interface AuthTokens {
  accessToken?: string;
  idToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  tokenType?: string;
  issuedAt?: number;
}

// Define the shape of our user profile
export interface UserProfile {
  sub?: string;
  name?: string;
  email?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
}

// Storage keys - using only alphanumeric characters, ".", "-", and "_" for SecureStore compatibility
const AUTH_TOKENS_KEY = "eneco_auth_tokens";
const USER_PROFILE_KEY = "eneco_user_profile";
const PKCE_CODE_VERIFIER_KEY = "eneco_pkce_code_verifier";

// Okta configuration
// Static discovery object for Okta endpoints

// For development, we can also use a static discovery object based on the actual Eneco Okta setup
const staticDiscovery = {
  authorizationEndpoint: `https://inloggen.acc.eneco.nl/oauth2/default/v1/authorize`,
  tokenEndpoint: `https://inloggen.acc.eneco.nl/oauth2/default/v1/token`,
  revocationEndpoint: `https://inloggen.acc.eneco.nl/oauth2/default/v1/revoke`,
  endSessionEndpoint: `https://inloggen.acc.eneco.nl/oauth2/default/v1/logout`,
  userInfoEndpoint: `https://inloggen.acc.eneco.nl/oauth2/default/v1/userinfo`,
  jwksUri: `https://inloggen.acc.eneco.nl/oauth2/default/v1/keys`,
  issuer: `https://inloggen.acc.eneco.nl/oauth2/default`,
};

// Save PKCE code verifier
export const savePKCECodeVerifier = async (codeVerifier: string) => {
  try {
    if (Platform.OS !== "web") {
      // Use SecureStore for native platforms
      console.log("Saving PKCE code verifier to SecureStore");
      await SecureStore.setItemAsync(PKCE_CODE_VERIFIER_KEY, codeVerifier);
    } else {
      // Use localStorage for web
      console.log("Saving PKCE code verifier to localStorage");
      localStorage.setItem(PKCE_CODE_VERIFIER_KEY, codeVerifier);
    }
  } catch (error) {
    console.error("Error saving PKCE code verifier:", error);
    throw error;
  }
};

// Get PKCE code verifier
export const getPKCECodeVerifier = async (): Promise<string | null> => {
  try {
    let codeVerifier = null;

    if (Platform.OS !== "web") {
      // Use SecureStore for native platforms
      codeVerifier = await SecureStore.getItemAsync(PKCE_CODE_VERIFIER_KEY);
    } else {
      // Use localStorage for web
      codeVerifier = localStorage.getItem(PKCE_CODE_VERIFIER_KEY);
    }

    if (codeVerifier) {
      console.log("Retrieved PKCE code verifier");
      return codeVerifier;
    }

    console.log("No PKCE code verifier found in storage");
    return null;
  } catch (error) {
    console.error("Error retrieving PKCE code verifier:", error);
    return null;
  }
};

// Get the auth request configuration
export const getAuthRequestConfig = () => {
  // Use the static discovery object for Okta endpoints
  const discovery = staticDiscovery;

  // Generate the redirect URI based on platform
  // Important: This MUST match the redirect URI used in the token exchange
  // and must be registered in the Okta application settings
  let redirectUri;

  if (Platform.OS === "web") {
    // For web, use the environment-specific redirect URI
    redirectUri = env.oktaRedirectUri;

    // Log the redirect URI for debugging
    console.log("Using web redirect URI for auth request:", redirectUri);
  } else {
    // For native platforms, use the Eneco-specific URL scheme
    redirectUri = "eneco-acc.okta-emea.com:/callback";

    // Log the redirect URI for debugging
    console.log("Using native redirect URI for auth request:", redirectUri);
  }

  // Log debugging information
  console.log("Platform:", Platform.OS);
  console.log("Redirect URI:", redirectUri);
  console.log("Using discovery:", discovery);
  console.log("Okta client ID:", env.oktaClientId);

  // Create the auth request configuration
  const request = {
    clientId: env.oktaClientId,
    // Request these scopes from Okta (matching mobile app configuration)
    scopes: ["openid", "profile", "offline_access"],
    // Use the appropriate redirect URI
    redirectUri,
    // Force login screen to appear
    prompt: AuthSession.Prompt.Login,
    // Use authorization code flow
    responseType: AuthSession.ResponseType.Code,
    // Enable PKCE (Proof Key for Code Exchange) for added security
    usePKCE: true,
  };

  console.log("Created auth request config:", request);

  return { request, discovery };
};

// This is a React hook that must be used within a React component
export const useAuthRequest = (): [
  AuthSession.AuthRequest | null,
  AuthSession.AuthSessionResult | null,
  (options?: AuthSession.AuthRequestPromptOptions) => Promise<AuthSession.AuthSessionResult>
] => {
  // Get the auth request configuration
  const { request, discovery } = getAuthRequestConfig();

  // Create the auth request using Expo's useAuthRequest hook
  const [authRequest, authResponse, promptAsync] = AuthSession.useAuthRequest(request, discovery);

  // Store the code verifier when the request is created
  useEffect(() => {
    if (authRequest?.codeVerifier) {
      console.log("Auth request created with code verifier, saving it");
      savePKCECodeVerifier(authRequest.codeVerifier);
    } else {
      console.log("Auth request created but no code verifier found");
    }
  }, [authRequest]);

  return [authRequest, authResponse, promptAsync];
};

// Warm up the browser
export const warmUpBrowser = async () => {
  await WebBrowser.warmUpAsync();
};

// Cool down the browser
export const coolDownBrowser = async () => {
  await WebBrowser.coolDownAsync();
};

// Save tokens securely
export const saveTokens = async (tokens: AuthTokens) => {
  const tokenData = {
    ...tokens,
    issuedAt: Date.now(),
  };

  if (Platform.OS !== "web") {
    // Use SecureStore for native platforms
    console.log("saving tokens: ", tokenData);
    await SecureStore.setItemAsync(AUTH_TOKENS_KEY, JSON.stringify(tokenData));
  } else {
    // Use localStorage for web
    localStorage.setItem(AUTH_TOKENS_KEY, JSON.stringify(tokenData));
  }

  // Log the tokens for debugging
  console.log("Saving tokens:", {
    accessToken: tokens.accessToken
      ? `${tokens.accessToken.substring(0, 10)}...`
      : null,
    idToken: tokens.idToken ? `${tokens.idToken.substring(0, 10)}...` : null,
    refreshToken: tokens.refreshToken ? "present" : "not present",
    expiresIn: tokens.expiresIn,
  });
};

// Get tokens
export const getTokens = async (): Promise<AuthTokens | null> => {
  let tokensString = null;

  if (Platform.OS !== "web") {
    // Use SecureStore for native platforms
    tokensString = await SecureStore.getItemAsync(AUTH_TOKENS_KEY);
  } else {
    // Use localStorage for web
    tokensString = localStorage.getItem(AUTH_TOKENS_KEY);
  }

  if (tokensString) {
    const tokens = JSON.parse(tokensString);

    // Log the retrieved tokens for debugging
    console.log("Retrieved tokens:", {
      accessToken: tokens.accessToken
        ? `${tokens.accessToken.substring(0, 10)}...`
        : null,
      idToken: tokens.idToken ? `${tokens.idToken.substring(0, 10)}...` : null,
      refreshToken: tokens.refreshToken ? "present" : "not present",
      expiresIn: tokens.expiresIn,
      issuedAt: new Date(tokens.issuedAt).toISOString(),
    });

    return tokens;
  }

  console.log("No tokens found in storage");
  return null;
};

// Save user profile
export const saveUserProfile = async (profile: UserProfile) => {
  if (Platform.OS !== "web") {
    // Use SecureStore for native platforms
    await SecureStore.setItemAsync(USER_PROFILE_KEY, JSON.stringify(profile));
  } else {
    // Use localStorage for web
    localStorage.setItem(USER_PROFILE_KEY, JSON.stringify(profile));
  }

  // Log the user profile for debugging
  console.log("Saving user profile:", {
    sub: profile.sub,
    name: profile.name,
    email: profile.email,
    preferred_username: profile.preferred_username,
  });
};

// Get user profile
export const getUserProfile = async (): Promise<UserProfile | null> => {
  let profileString = null;

  if (Platform.OS !== "web") {
    // Use SecureStore for native platforms
    profileString = await SecureStore.getItemAsync(USER_PROFILE_KEY);
  } else {
    // Use localStorage for web
    profileString = localStorage.getItem(USER_PROFILE_KEY);
  }

  if (profileString) {
    const profile = JSON.parse(profileString);

    // Log the retrieved user profile for debugging
    console.log("Retrieved user profile:", {
      sub: profile.sub,
      name: profile.name,
      email: profile.email,
      preferred_username: profile.preferred_username,
    });

    return profile;
  }

  console.log("No user profile found in storage");
  return null;
};

// Clear auth data
export const clearAuthData = async () => {
  if (Platform.OS !== "web") {
    // Use SecureStore for native platforms
    await SecureStore.deleteItemAsync(AUTH_TOKENS_KEY);
    await SecureStore.deleteItemAsync(USER_PROFILE_KEY);
  } else {
    // Use localStorage for web
    localStorage.removeItem(AUTH_TOKENS_KEY);
    localStorage.removeItem(USER_PROFILE_KEY);
  }

  console.log("Auth data cleared");
};

// Logout from Okta
export const logout = async () => {
  try {
    console.log("Starting logout process");

    // Get the current tokens to check if we have an ID token for logout
    const tokens = await getTokens();

    // Clear local auth data first
    await clearAuthData();

    // If we have an ID token, perform server-side logout
    if (tokens?.idToken) {
      // Generate the logout redirect URI based on platform
      let logoutRedirectUri;

      if (Platform.OS === "web") {
        // For web, use the environment-specific logout redirect URI
        logoutRedirectUri = env.oktaLogoutRedirectUri;
      } else {
        // For native platforms, use the Eneco-specific logout URL scheme
        logoutRedirectUri = "eneco-acc.okta-emea.com:/logout";
      }

      // Construct the logout URL
      const logoutUrl = `${staticDiscovery.endSessionEndpoint}?id_token_hint=${tokens.idToken}&post_logout_redirect_uri=${encodeURIComponent(logoutRedirectUri)}`;

      console.log("Logout URL:", logoutUrl);

      // Open the logout URL in the browser
      if (Platform.OS === "web") {
        // For web, redirect the current window
        window.location.href = logoutUrl;
      } else {
        // For native platforms, open in browser
        await WebBrowser.openBrowserAsync(logoutUrl);
      }
    }

    console.log("Logout completed");
  } catch (error) {
    console.error("Logout error:", error);
    // Even if logout fails, clear local data
    await clearAuthData();
  }
};

// Exchange code for tokens
export const exchangeCodeForTokens = async (
  code: string
): Promise<AuthTokens> => {
  try {
    console.log(`Exchanging authorization code for tokens`);

    // Get the redirect URI that was used for the authorization request
    // Important: This MUST match the redirect URI used in the authorization request
    // and must be registered in the Okta application settings
    let redirectUri;
    if (Platform.OS === "web") {
      // For web, use the environment-specific redirect URI
      redirectUri = env.oktaRedirectUri;

      // Log the redirect URI for debugging
      console.log("Using web redirect URI:", redirectUri);
    } else {
      // For native platforms, use the Eneco-specific URL scheme
      redirectUri = "eneco-acc.okta-emea.com:/callback";

      // Log the redirect URI for debugging
      console.log("Using native redirect URI:", redirectUri);
    }

    // Retrieve the PKCE code verifier that was stored during the authorization request
    const codeVerifier = await getPKCECodeVerifier();

    if (!codeVerifier) {
      console.error("PKCE code verifier not found in storage");
      throw new Error("PKCE code verifier is missing. Please try logging in again.");
    }

    console.log("Retrieved PKCE code verifier for token exchange");

    // Use AuthSession.exchangeCodeAsync to exchange the code for tokens
    console.log(`Using AuthSession.exchangeCodeAsync to exchange code for tokens`);

    // Exchange the code for tokens using Expo's AuthSession
    const tokenResult = await AuthSession.exchangeCodeAsync(
      {
        clientId: env.oktaClientId,
        code,
        redirectUri,
        extraParams: {
          code_verifier: codeVerifier, // Explicitly provide the code verifier
        },
      },
      staticDiscovery
    );

    console.log("Token exchange successful");
    console.log("Token response received");

    // Create the tokens object
    const tokens: AuthTokens = {
      accessToken: tokenResult.accessToken,
      idToken: tokenResult.idToken,
      refreshToken: tokenResult.refreshToken,
      expiresIn: tokenResult.expiresIn,
      tokenType: tokenResult.tokenType,
      issuedAt: Date.now(),
    };

    // Log token information (without revealing the full tokens)
    console.log("Received tokens:", {
      accessToken: tokens.accessToken
        ? `${tokens.accessToken.substring(0, 10)}...`
        : null,
      idToken: tokens.idToken ? `${tokens.idToken.substring(0, 10)}...` : null,
      refreshToken: tokens.refreshToken ? "present" : "not present",
      expiresIn: tokens.expiresIn,
      tokenType: tokens.tokenType,
    });

    // Save the tokens
    await saveTokens(tokens);

    // Clear the PKCE code verifier after successful token exchange
    if (Platform.OS !== "web") {
      await SecureStore.deleteItemAsync(PKCE_CODE_VERIFIER_KEY);
    } else {
      localStorage.removeItem(PKCE_CODE_VERIFIER_KEY);
    }

    return tokens;
  } catch (error) {
    console.error("Error exchanging code for tokens:", error);
    throw error;
  }
};

// Get user info from ID token
export const getUserInfo = async (idToken: string): Promise<UserProfile> => {
  try {
    if (!idToken) {
      console.log("No ID token received");
    }

    console.log(`Processing ID token: ${idToken.substring(0, 10)}...`);

    // Option 1: Decode the JWT token locally
    // This is a simple JWT decoder that doesn't verify the signature
    // In a production app, you should use a proper JWT library that verifies the signature
    const tokenParts = idToken.split(".");
    if (tokenParts.length !== 3) {
      throw new Error("Invalid ID token format");
    }

    // Decode the payload (second part of the JWT)
    const payload = JSON.parse(atob(tokenParts[1]));
    console.log("Decoded ID token payload:", payload);

    // Extract user info from the payload
    const profile: UserProfile = {
      sub: payload.sub || "",
      name: payload.name || "",
      email: payload.email || "",
      preferred_username: payload.preferred_username || "",
      given_name: payload.given_name || "",
      family_name: payload.family_name || "",
    };

    // Option 2: Call the userinfo endpoint with the access token
    // This is an alternative approach if you prefer to get user info from the server
    // const userInfoEndpoint = staticDiscovery.userInfoEndpoint;
    // const accessToken = (await getTokens())?.accessToken;
    // if (accessToken) {
    //   const response = await fetch(userInfoEndpoint, {
    //     headers: {
    //       Authorization: `Bearer ${accessToken}`,
    //     },
    //   });
    //   if (response.ok) {
    //     const userInfo = await response.json();
    //     profile = {
    //       sub: userInfo.sub || '',
    //       name: userInfo.name || '',
    //       email: userInfo.email || '',
    //       preferred_username: userInfo.preferred_username || '',
    //       given_name: userInfo.given_name || '',
    //       family_name: userInfo.family_name || '',
    //     };
    //   }
    // }

    console.log("User profile from ID token:", {
      sub: profile.sub,
      name: profile.name,
      email: profile.email,
      preferred_username: profile.preferred_username,
    });

    await saveUserProfile(profile);
    return profile;
  } catch (error) {
    console.error("Error getting user info:", error);
    throw error;
  }
};

// Check if tokens are valid
export const isTokenValid = (tokens: AuthTokens): boolean => {
  // Check if required token properties exist
  if (!tokens.accessToken || !tokens.issuedAt || !tokens.expiresIn) {
    console.log("Token validation failed: missing required properties");
    return false;
  }

  // Calculate expiration time
  const expirationTime = tokens.issuedAt + tokens.expiresIn * 1000;
  const now = Date.now();
  const isValid = now < expirationTime;

  // Log token validity details
  console.log("Token validation:", {
    isValid,
    expiresIn: tokens.expiresIn,
    issuedAt: new Date(tokens.issuedAt).toISOString(),
    expirationTime: new Date(expirationTime).toISOString(),
    currentTime: new Date(now).toISOString(),
    timeRemaining: Math.floor((expirationTime - now) / 1000) + " seconds",
  });

  return isValid;
};

// Initialize WebBrowser
// This is important to call at the top level to properly handle redirects
WebBrowser.maybeCompleteAuthSession();

// Initialize the auth session
export const initializeAuth = () => {
  // Log that we're initializing auth
  console.log("Initializing Okta authentication");

  // Make sure WebBrowser is properly initialized
  WebBrowser.maybeCompleteAuthSession();
};
