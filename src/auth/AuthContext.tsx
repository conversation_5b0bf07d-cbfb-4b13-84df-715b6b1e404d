import { AuthSessionResult } from 'expo-auth-session';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import env from '../config/env';
import * as OktaService from './OktaService';
import { UserProfile } from './OktaService';

// Define the shape of our authentication state
type AuthState = {
  isAuthenticated: boolean;
  user: UserProfile | null;
};

// Define the shape of our context
type AuthContextType = {
  authState: AuthState;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  promptAsync: () => Promise<AuthSessionResult | null>;
};

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Create auth request using the hook
  const [request, response, promptAsync] = OktaService.useAuthRequest();

  // Log the current environment
  useEffect(() => {
    console.log(`Current environment: ${env.environment}`);
    console.log(`Okta domain: ${env.oktaDomain}`);
    console.log(`Okta redirect URI: ${env.oktaRedirectUri}`);
  }, []);

  // Log when the request is ready
  useEffect(() => {
    if (request) {
      console.log('Auth request is ready');
    } else {
      console.log('Auth request is not ready yet');
    }
  }, [request]);

  // Handle auth response
  useEffect(() => {
    const handleAuthResponse = async () => {
      // Check if response is an AuthSessionResult (not null and not the AuthRequest or promptAsync function)
      if (response && 'type' in response) {
        console.log("Auth response received:", response.type);

        if (response.type === 'success' && 'params' in response && response.params.code) {
          setIsLoading(true);
          try {
            console.log("Successful auth response with code");

            // Exchange code for tokens
            const tokens = await OktaService.exchangeCodeForTokens(response.params.code);

            // Get user info from ID token
            if (tokens.idToken) {
              const userProfile = await OktaService.getUserInfo(tokens.idToken);

              // Update auth state
              setAuthState({
                isAuthenticated: true,
                user: userProfile,
              });

              console.log("Authentication successful, user profile loaded");
            }
          } catch (error) {
            console.error('Authentication error:', error);
          } finally {
            setIsLoading(false);
          }
        } else if (response.type === 'error' && 'error' in response) {
          console.error('Authentication error:', response.error);
          setIsLoading(false);
        } else {
          console.log("Other response type:", response.type);
          // Only log params if they exist
          if ('params' in response) {
            console.log("Response params:", response.params);
          }
        }
      }
    };

    handleAuthResponse();
  }, [response]);

  // Load auth state on mount
  useEffect(() => {
    const loadAuthState = async () => {
      try {
        console.log("Loading auth state on mount");

        // Get tokens from secure storage
        const tokens = await OktaService.getTokens();

        if (tokens) {
          console.log("Found tokens in storage");

          // Check if tokens are valid
          const isValid = OktaService.isTokenValid(tokens);
          console.log("Tokens valid:", isValid);

          if (isValid) {
            // Get user profile
            const userProfile = await OktaService.getUserProfile();

            if (userProfile) {
              console.log("Found user profile in storage:", userProfile.name);

              // Update auth state
              setAuthState({
                isAuthenticated: true,
                user: userProfile,
              });

              console.log("Auth state updated, user is authenticated");
            } else {
              console.log("No user profile found, trying to get user info from ID token");

              // Try to get user info from ID token
              if (tokens.idToken) {
                try {
                  const userProfile = await OktaService.getUserInfo(tokens.idToken);

                  // Update auth state
                  setAuthState({
                    isAuthenticated: true,
                    user: userProfile,
                  });

                  console.log("Auth state updated with new user profile");
                } catch (error) {
                  console.error("Error getting user info from ID token:", error);
                }
              }
            }
          } else {
            console.log("Tokens are invalid or expired, clearing auth data");
            await OktaService.clearAuthData();
          }
        } else {
          console.log("No tokens found in storage");
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthState();
  }, []);

  // Login function
  const login = async () => {
    console.log("Login function called");
    try {
      setIsLoading(true);

      // Ensure the browser is warmed up before prompting
      if (Platform.OS !== 'web') {
        console.log("Warming up browser");
        await OktaService.warmUpBrowser();
      }

      console.log("Browser warmed up, calling safePromptAsync");

      // Use the safe prompt function which checks if request is ready
      await safePromptAsync();
    } catch (error) {
      console.error('Login error:', error);
      setIsLoading(false);
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);

      // Use the Okta logout function which handles both local and server-side logout
      await OktaService.logout();

      // Update auth state
      setAuthState({
        isAuthenticated: false,
        user: null,
      });
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Create a safe promptAsync function that checks if request is ready
  const safePromptAsync = async () => {
    console.log("safePromptAsync called");
    console.log("Request state:", request ? "ready" : "not ready");

    if (!request) {
      console.error("Auth request is not ready yet");
      throw new Error('Auth request is not ready yet. Please try again in a moment.');
    }

    // Check if promptAsync is a function
    if (typeof promptAsync === 'function') {
      console.log("Calling promptAsync");
      return promptAsync();
    } else {
      console.error("promptAsync is not a function");
      throw new Error('Authentication function is not available. Please try again in a moment.');
    }
  };

  // Provide the auth context to children components
  return (
    <AuthContext.Provider value={{
      authState,
      login,
      logout,
      isLoading,
      promptAsync: safePromptAsync
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
