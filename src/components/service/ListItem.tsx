/**
 * List Item component for the Service tab
 */

import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { Text } from '../../design-system/components/Text';
import { useAppTheme } from '../../design-system/provider';
import Icon from '../Icon';

interface ListItemProps {
  /** Icon name for the left icon */
  leftIcon?: string;
  /** Icon type for the left icon */
  leftIconType?: 'material' | 'material-community' | 'fontawesome';
  /** Title of the list item */
  title: string;
  /** Subtitle of the list item */
  subtitle?: string;
  /** Icon name for the right icon */
  rightIcon?: string;
  /** Icon type for the right icon */
  rightIconType?: 'material' | 'material-community' | 'fontawesome';
  /** Badge count to display */
  badge?: number;
  /** Function to call when the list item is pressed */
  onPress?: () => void;
  /** Whether this is the last item in the list */
  isLast?: boolean;
}

/**
 * List Item component for the Service tab
 */
export const ListItem: React.FC<ListItemProps> = ({
  leftIcon,
  leftIconType = 'material',
  title,
  subtitle,
  rightIcon = 'chevron-right',
  rightIconType = 'material',
  badge,
  onPress,
  isLast = false,
}) => {
  const { theme } = useAppTheme();

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        {
          backgroundColor: pressed
            ? 'rgba(0, 0, 0, 0.05)'
            : 'transparent',
          borderBottomWidth: isLast ? 0 : StyleSheet.hairlineWidth,
          borderBottomColor: 'rgba(0, 0, 0, 0.1)',
        },
      ]}
      onPress={onPress}
      android_ripple={{ color: 'rgba(0, 0, 0, 0.05)' }}
    >
      {/* Left Icon */}
      {leftIcon && (
        <View style={styles.leftIconContainer}>
          <Icon
            name={leftIcon}
            type={leftIconType}
            size={24}
            color={theme.colors.textPrimary}
          />
        </View>
      )}

      {/* Content */}
      <View style={styles.contentContainer}>
        <Text
          variant="bodyLarge"
          style={{ color: theme.colors.textPrimary }}
        >
          {title}
        </Text>

        {subtitle && (
          <Text
            variant="bodyMedium"
            style={{ color: 'rgba(47, 45, 45, 0.7)' }}
          >
            {subtitle}
          </Text>
        )}
      </View>

      {/* Badge */}
      {badge !== undefined && badge > 0 && (
        <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
          <Text
            variant="labelSmall"
            style={{ color: theme.colors.onPrimary }}
          >
            {badge}
          </Text>
        </View>
      )}

      {/* Right Icon */}
      {rightIcon && (
        <View style={styles.rightIconContainer}>
          <Icon
            name={rightIcon}
            type={rightIconType}
            size={20}
            color="rgba(47, 45, 45, 0.7)"
          />
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  leftIconContainer: {
    marginRight: 16,
  },
  contentContainer: {
    flex: 1,
  },
  rightIconContainer: {
    marginLeft: 8,
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    marginRight: 8,
  },
});

export default ListItem;
