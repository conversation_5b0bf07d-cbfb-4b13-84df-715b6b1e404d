/**
 * Section Header component for the Service tab
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from '../../design-system/components/Text';
import { useAppTheme } from '../../design-system/provider';

interface SectionHeaderProps {
  /** Title of the section */
  title: string;
}

/**
 * Section Header component for the Service tab
 */
export const SectionHeader: React.FC<SectionHeaderProps> = ({ title }) => {
  const { theme } = useAppTheme();

  return (
    <View style={styles.container}>
      <Text
        variant="titleMedium"
        style={[styles.title, { color: theme.colors.textPrimary }]}
      >
        {title}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontWeight: '600',
  },
});

export default SectionHeader;
