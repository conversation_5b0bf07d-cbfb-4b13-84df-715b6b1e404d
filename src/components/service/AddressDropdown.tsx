/**
 * Address Dropdown component for the Service tab
 */

import React, { useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { Text } from '../../design-system/components/Text';
import { useAppTheme } from '../../design-system/provider';
import Icon from '../Icon';

interface AddressDropdownProps {
  /** Address to display */
  address: string;
}

/**
 * Address Dropdown component for the Service tab
 */
export const AddressDropdown: React.FC<AddressDropdownProps> = ({ address }) => {
  const { theme } = useAppTheme();
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <View style={styles.container}>
      <Pressable
        style={({ pressed }) => [
          styles.dropdownButton,
          {
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            borderColor: pressed ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)',
          },
        ]}
        onPress={toggleDropdown}
        android_ripple={{ color: 'rgba(0, 0, 0, 0.05)' }}
      >
        <Text
          variant="bodyLarge"
          style={{ color: theme.colors.textPrimary }}
        >
          {address}
        </Text>

        <Icon
          name={isOpen ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
          size={24}
          color={theme.colors.textPrimary}
        />
      </Pressable>

      {isOpen && (
        <View style={[styles.dropdownContent, { backgroundColor: theme.colors.backgroundSecondary }]}>
          <Pressable
            style={({ pressed }) => [
              styles.dropdownItem,
              {
                backgroundColor: pressed ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
              },
            ]}
            onPress={() => setIsOpen(false)}
          >
            <Text
              variant="bodyMedium"
              style={{ color: theme.colors.textPrimary }}
            >
              {address}
            </Text>
            <Icon
              name="check"
              size={20}
              color={theme.colors.primary}
            />
          </Pressable>

          <Pressable
            style={({ pressed }) => [
              styles.dropdownItem,
              {
                backgroundColor: pressed ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
              },
            ]}
            onPress={() => setIsOpen(false)}
          >
            <Text
              variant="bodyMedium"
              style={{ color: theme.colors.textPrimary }}
            >
              Add new address
            </Text>
            <Icon
              name="add"
              size={20}
              color={theme.colors.textPrimary}
            />
          </Pressable>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  dropdownContent: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderRadius: 8,
    marginTop: 4,
    overflow: 'hidden',
    zIndex: 2,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
});

export default AddressDropdown;
