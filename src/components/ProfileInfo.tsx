/**
 * Profile Info Component
 *
 * This component demonstrates how to use the Digital Core API to fetch and display user profile information.
 */

import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { useDigitalCore } from '../api/hooks/useDigitalCore';
import { ProfileResponse } from '../api/types';
import { tokens } from '../design-system';
import { Heading, Paragraph } from '../design-system/components';

/**
 * Profile Info Component
 */
export const ProfileInfo: React.FC = () => {
  const { isLoading, error, getProfile } = useDigitalCore();
  const [profile, setProfile] = useState<ProfileResponse | null>(null);

  console.log(JSON.stringify(profile, null, 2));
  // Fetch profile on mount
  useEffect(() => {
    const fetchProfile = async () => {
      const profileData = await getProfile();
      console.log("###");
      console.log("Profile Data:", profileData);
      console.log("###");
      if (profileData) {
        setProfile(profileData);
      }
    };

    fetchProfile();
  }, [getProfile]);

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={tokens.colors.secondaryColors.accentGreen700}
          style={styles.loader}
        />
        <Paragraph>Loading profile information...</Paragraph>
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <Heading as="h2" size="S" style={styles.heading}>
          Error Loading Profile
        </Heading>
        <Paragraph style={styles.errorText}>
          {error.message || 'An error occurred while loading your profile.'}
        </Paragraph>
      </View>
    );
  }

  // Render profile information
  return (
    <View style={styles.container}>
      {profile?.data ? (
        <>
          <Heading as="h2" size="S" style={styles.heading}>
            Profile Information
          </Heading>

          <View style={styles.infoSection}>
            <Heading as="h3" size="XS">Personal Details</Heading>
            {profile.data.person ? (
              <>
                <Paragraph>Name: {profile.data.person.name || 'N/A'}</Paragraph>
                <Paragraph>First Name: {profile.data.person.firstName || 'N/A'}</Paragraph>
                <Paragraph>Initials: {profile.data.person.initials || 'N/A'}</Paragraph>
                <Paragraph>Surname: {profile.data.person.surname || 'N/A'}</Paragraph>
                <Paragraph>Surname Preposition: {profile.data.person.surnamePreposition || 'N/A'}</Paragraph>
                <Paragraph>Gender: {profile.data.person.gender || 'N/A'}</Paragraph>
                <Paragraph>Salutation: {profile.data.person.salutation || 'N/A'}</Paragraph>
                <Paragraph>Date of Birth: {profile.data.person.dateOfBirth || 'N/A'}</Paragraph>
              </>
            ) : profile.data.organisation ? (
              <>
                <Paragraph>Organization Name: {profile.data.organisation.name || 'N/A'}</Paragraph>
                <Paragraph>KVK: {profile.data.organisation.kvk || 'N/A'}</Paragraph>
              </>
            ) : (
              <Paragraph>Name: {profile.data.contact?.name || 'N/A'}</Paragraph>
            )}
            <Paragraph>Email: {profile.data.contact?.emailAddress || 'N/A'}</Paragraph>
            <Paragraph>Phone: {profile.data.contact?.phoneNumber || 'N/A'}</Paragraph>
            <Paragraph>Mobile: {profile.data.contact?.mobilePhoneNumber || 'N/A'}</Paragraph>
          </View>

          <View style={styles.infoSection}>
            <Heading as="h3" size="XS">Address</Heading>
            {profile.data.contact?.address ? (
              <>
                <Paragraph>
                  Street: {profile.data.contact.address.street || 'N/A'}
                </Paragraph>
                <Paragraph>
                  House Number: {profile.data.contact.address.houseNumber || 'N/A'}
                  {profile.data.contact.address.houseNumberSuffix ? ` ${profile.data.contact.address.houseNumberSuffix}` : ''}
                </Paragraph>
                <Paragraph>
                  Postal Code: {profile.data.contact.address.postalCode || 'N/A'}
                </Paragraph>
                <Paragraph>
                  City: {profile.data.contact.address.city || 'N/A'}
                </Paragraph>
                <Paragraph>
                  Bus: {profile.data.contact.address.bus || 'N/A'}
                </Paragraph>
              </>
            ) : (
              <Paragraph>No address information available</Paragraph>
            )}
          </View>

          <View style={styles.infoSection}>
            <Heading as="h3" size="XS">Account Information</Heading>
            <Paragraph>Customer ID: {profile.data.customerId || 'N/A'}</Paragraph>
            <Paragraph>Customer Type: {profile.data.customerType || 'N/A'}</Paragraph>
            <Paragraph>Username: {profile.data.userAccount?.userName || 'N/A'}</Paragraph>
            <Paragraph>Last Login: {profile.data.userAccount?.lastLogin || 'N/A'}</Paragraph>
            <Paragraph>Features: {profile.data.features?.join(', ') || 'N/A'}</Paragraph>
            <Paragraph>Has Relocation: {profile.data.relocationInfo?.hasRelocation ? 'Yes' : 'No'}</Paragraph>
            <Paragraph>Move Out Date: {profile.data.relocationInfo?.moveOutDateSpecified || 'N/A'}</Paragraph>
          </View>

          {profile.data.accounts?.length > 0 && (
            <View style={styles.infoSection}>
              <Heading as="h3" size="XS">Energy Accounts</Heading>
              {profile.data.accounts?.map((account) => (
                <View key={account?.accountId} style={styles.accountItem}>
                  <Paragraph>Account ID: {account?.accountId}</Paragraph>
                  <Paragraph>
                    Products: {account?.productTypes.join(', ')}
                  </Paragraph>
                  <Paragraph>
                    Start Date: {account?.startDate}
                  </Paragraph>
                  <Paragraph>
                    End Date: {account?.endDate || 'N/A'}
                  </Paragraph>
                  <Paragraph>
                    Dynamic Pricing: {account?.hasDynamicPricing ? 'Yes' : 'No'}
                  </Paragraph>
                  <Paragraph>
                    Active: {account?.active ? 'Yes' : 'No'}
                  </Paragraph>
                  <Paragraph>
                    Customer Profile Type: {account?.customerProfileType || 'N/A'}
                  </Paragraph>
                  <Paragraph>
                    Next Charge Date: {account?.nextChargeDate || 'N/A'}
                  </Paragraph>
                  <Paragraph>
                    Has Redelivery: {account?.hasRedelivery ? 'Yes' : 'No'}
                  </Paragraph>
                  <Paragraph>
                    Has Redelivery Cost Tariff: {account?.hasRedeliveryCostTariff ? 'Yes' : 'No'}
                  </Paragraph>
                  <Paragraph>
                    Has Service Contract: {account?.hasServiceContract ? 'Yes' : 'No'}
                  </Paragraph>

                  {account?.address && (
                    <View style={styles.nestedSection}>
                      <Heading as="h4" size="XS">Account Address</Heading>
                      <Paragraph>
                        {account.address.street} {account.address.houseNumber}
                        {account.address.houseNumberSuffix ? ` ${account.address.houseNumberSuffix}` : ''}
                      </Paragraph>
                      <Paragraph>
                        {account.address.postalCode} {account.address.city}
                      </Paragraph>
                    </View>
                  )}

                  {account?.meterDetails?.length > 0 && (
                    <View style={styles.nestedSection}>
                      <Heading as="h4" size="XS">Meter Details</Heading>
                      {account.meterDetails.map((meter, index) => (
                        <View key={index} style={styles.meterItem}>
                          <Paragraph>Product Type: {meter.productType}</Paragraph>
                          <Paragraph>Smart Meter: {meter.isSmartMeter ? 'Yes' : 'No'}</Paragraph>
                          <Paragraph>Smart Meter Reading Allowed: {meter.isSmartMeterReadingAllowed ? 'Yes' : 'No'}</Paragraph>
                        </View>
                      ))}
                    </View>
                  )}

                  {account?.productTypeDetails?.length > 0 && (
                    <View style={styles.nestedSection}>
                      <Heading as="h4" size="XS">Product Details</Heading>
                      {account.productTypeDetails.map((product, index) => (
                        <View key={index} style={styles.productItem}>
                          <Paragraph>Product Type: {product.productType}</Paragraph>
                          <Paragraph>Dynamic Pricing: {product.isDynamicPricing ? 'Yes' : 'No'}</Paragraph>
                          <Paragraph>Price Differentiation: {product.priceDifferentiation}</Paragraph>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
        </>
      ) : (
        <Paragraph>No profile information available.</Paragraph>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: tokens.spacing.space[4],
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    borderRadius: tokens.borders.radii.m,
    marginBottom: tokens.spacing.space[4],
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
  },
  loader: {
    marginVertical: tokens.spacing.space[4],
  },
  errorText: {
    color: tokens.colors.feedbackColors.feedbackError,
  },
  infoSection: {
    marginBottom: tokens.spacing.space[4],
  },
  accountItem: {
    marginTop: tokens.spacing.space[2],
    paddingTop: tokens.spacing.space[2],
    borderTopWidth: 1,
    borderTopColor: tokens.colors.borderColors.borderDividerLowEmphasis,
  },
  nestedSection: {
    marginTop: tokens.spacing.space[3],
    paddingTop: tokens.spacing.space[2],
    paddingLeft: tokens.spacing.space[2],
    borderLeftWidth: 2,
    borderLeftColor: tokens.colors.borderColors.borderDividerLowEmphasis,
  },
  meterItem: {
    marginTop: tokens.spacing.space[2],
    paddingTop: tokens.spacing.space[1],
    borderTopWidth: 1,
    borderTopColor: tokens.colors.borderColors.borderDividerLowEmphasis,
    paddingLeft: tokens.spacing.space[2],
  },
  productItem: {
    marginTop: tokens.spacing.space[2],
    paddingTop: tokens.spacing.space[1],
    borderTopWidth: 1,
    borderTopColor: tokens.colors.borderColors.borderDividerLowEmphasis,
    paddingLeft: tokens.spacing.space[2],
  },
});
