/**
 * Icon component for the app
 */

import React from 'react';
import { StyleProp, TextStyle } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons, FontAwesome5 } from '@expo/vector-icons';
import { useAppTheme } from '../design-system/provider';

// Icon types
export type IconType = 'material' | 'material-community' | 'fontawesome';

// Icon props
export interface IconProps {
  /** Name of the icon */
  name: string;
  /** Type of icon library to use */
  type?: IconType;
  /** Size of the icon */
  size?: number;
  /** Color of the icon (overrides theme color) */
  color?: string;
  /** Style for the icon */
  style?: StyleProp<TextStyle>;
}

/**
 * Icon component that uses various icon libraries
 */
export const Icon: React.FC<IconProps> = ({
  name,
  type = 'material',
  size = 24,
  color,
  style,
}) => {
  const { theme } = useAppTheme();
  
  // Default color from theme
  const iconColor = color || theme.colors.neutralWhite;
  
  // Render the appropriate icon based on type
  switch (type) {
    case 'material-community':
      return (
        <MaterialCommunityIcons
          name={name as any}
          size={size}
          color={iconColor}
          style={style}
        />
      );
    case 'fontawesome':
      return (
        <FontAwesome5
          name={name as any}
          size={size}
          color={iconColor}
          style={style}
        />
      );
    case 'material':
    default:
      return (
        <MaterialIcons
          name={name as any}
          size={size}
          color={iconColor}
          style={style}
        />
      );
  }
};

export default Icon;
