/**
 * Meter Status Component
 * 
 * This component demonstrates how to use the Digital Core API to fetch and display meter status information.
 */

import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { useDigitalCore } from '../api/hooks/useDigitalCore';
import { MetersStatusResponse } from '../api/types';
import { tokens } from '../design-system';
import { Heading, Paragraph } from '../design-system/components';

interface MeterStatusProps {
  accountId: number;
}

/**
 * Meter Status Component
 */
export const MeterStatus: React.FC<MeterStatusProps> = ({ accountId }) => {
  const { isLoading, error, getMetersStatus } = useDigitalCore();
  const [meterStatus, setMeterStatus] = useState<MetersStatusResponse | null>(null);

  // Fetch meter status on mount
  useEffect(() => {
    const fetchMeterStatus = async () => {
      const statusData = await getMetersStatus(accountId);
      if (statusData) {
        setMeterStatus(statusData);
      }
    };

    fetchMeterStatus();
  }, [accountId, getMetersStatus]);

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={tokens.colors.secondaryColors.accentGreen700}
          style={styles.loader}
        />
        <Paragraph>Loading meter status...</Paragraph>
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <Heading as="h2" size="S" style={styles.heading}>
          Error Loading Meter Status
        </Heading>
        <Paragraph style={styles.errorText}>
          {error.message || 'An error occurred while loading meter status.'}
        </Paragraph>
      </View>
    );
  }

  // Render meter status information
  return (
    <View style={styles.container}>
      <Heading as="h2" size="S" style={styles.heading}>
        Meter Status
      </Heading>

      {meterStatus && meterStatus.meters.length > 0 ? (
        <View>
          {meterStatus.meters.map((meter, index) => (
            <View key={index} style={styles.meterItem}>
              <Heading as="h3" size="XS">
                {meter.utilityType}
              </Heading>
              <Paragraph>Grid Operator: {meter.gridOperator}</Paragraph>
              <Paragraph>
                Status: {meter.inError ? 'Error' : 'OK'}
              </Paragraph>
              {meter.inError && meter.errors.length > 0 && (
                <View style={styles.errorContainer}>
                  <Paragraph style={styles.errorText}>
                    Errors:
                  </Paragraph>
                  {meter.errors.map((error, errorIndex) => (
                    <Paragraph key={errorIndex} style={styles.errorText}>
                      {error}
                    </Paragraph>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>
      ) : (
        <Paragraph>No meter status information available.</Paragraph>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: tokens.spacing.space[4],
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    borderRadius: tokens.borders.radii.m,
    marginBottom: tokens.spacing.space[4],
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
  },
  loader: {
    marginVertical: tokens.spacing.space[4],
  },
  errorText: {
    color: tokens.colors.secondaryColors.red700,
  },
  meterItem: {
    marginBottom: tokens.spacing.space[3],
    paddingBottom: tokens.spacing.space[3],
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.borderColors.borderSubtle,
  },
  errorContainer: {
    marginTop: tokens.spacing.space[2],
    padding: tokens.spacing.space[2],
    backgroundColor: tokens.colors.secondaryColors.red100,
    borderRadius: tokens.borders.radii.s,
  },
});
