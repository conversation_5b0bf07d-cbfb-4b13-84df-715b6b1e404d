/**
 * Energy Usage Component
 * 
 * This component demonstrates how to use the Digital Core API to fetch and display energy usage information.
 */

import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { useDigitalCore } from '../api/hooks/useDigitalCore';
import { UsagesResponse } from '../api/types';
import { tokens } from '../design-system';
import { Heading, Paragraph } from '../design-system/components';

interface EnergyUsageProps {
  accountId: number;
}

/**
 * Energy Usage Component
 */
export const EnergyUsage: React.FC<EnergyUsageProps> = ({ accountId }) => {
  const { isLoading, error, getUsages } = useDigitalCore();
  const [usageData, setUsageData] = useState<UsagesResponse | null>(null);

  // Fetch usage data on mount
  useEffect(() => {
    const fetchUsageData = async () => {
      // Get current date
      const today = new Date();
      
      // Calculate start date (1 month ago)
      const startDate = new Date();
      startDate.setMonth(today.getMonth() - 1);
      
      // Format dates as ISO strings (YYYY-MM-DD)
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = today.toISOString().split('T')[0];
      
      const usages = await getUsages(accountId, {
        aggregation: 'Month',
        interval: 'Month',
        start: startDateStr,
        end: endDateStr,
        addWeather: true,
      });
      
      if (usages) {
        setUsageData(usages);
      }
    };

    fetchUsageData();
  }, [accountId, getUsages]);

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={tokens.colors.secondaryColors.accentGreen700}
          style={styles.loader}
        />
        <Paragraph>Loading energy usage data...</Paragraph>
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <Heading as="h2" size="S" style={styles.heading}>
          Error Loading Energy Usage
        </Heading>
        <Paragraph style={styles.errorText}>
          {error.message || 'An error occurred while loading energy usage data.'}
        </Paragraph>
      </View>
    );
  }

  // Render energy usage information
  return (
    <View style={styles.container}>
      <Heading as="h2" size="S" style={styles.heading}>
        Energy Usage
      </Heading>

      {usageData ? (
        <View>
          <Paragraph>
            Energy usage data is available. In a real implementation, this would display charts and detailed usage information.
          </Paragraph>
          <Paragraph>
            Raw data is available in the console for debugging.
          </Paragraph>
        </View>
      ) : (
        <Paragraph>No energy usage data available.</Paragraph>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: tokens.spacing.space[4],
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    borderRadius: tokens.borders.radii.m,
    marginBottom: tokens.spacing.space[4],
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
  },
  loader: {
    marginVertical: tokens.spacing.space[4],
  },
  errorText: {
    color: tokens.colors.secondaryColors.red700,
  },
});
