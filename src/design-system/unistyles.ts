/**
 * Unistyles configuration for the design system
 */

import { StyleSheet } from 'react-native-unistyles';
import tokens from './tokens';

// Define breakpoints
const breakpoints = {
  xs: 0,
  sm: 375,
  md: 768,
  lg: 1024,
  xl: 1280,
};

// Define light theme
const lightTheme = {
  colors: {
    ...tokens.colors.palette,
    // Add any light-theme specific overrides here
  },
  typography: {
    ...tokens.typography.bodyTypography,
    ...tokens.typography.headingTypography,
  },
  spacing: tokens.spacing.space,
  borders: {
    ...tokens.borders.borderWidths,
    ...tokens.borders.radii,
    ...tokens.borders.outlines,
    ...tokens.borders.componentBorders,
  },
  shadows: {
    ...tokens.shadows.boxShadows,
    ...tokens.shadows.borderShadows,
    ...tokens.shadows.componentShadows,
  },
  animations: {
    ...tokens.animation.transitions,
    ...tokens.animation.animations,
  },
};

// Define dark theme
const darkTheme = {
  colors: {
    // Invert some colors for dark theme
    ...tokens.colors.palette,
    backgroundPrimary: tokens.colors.neutralColors.neutral900,
    backgroundSecondary: tokens.colors.neutralColors.neutral800,
    textPrimary: tokens.colors.neutralColors.neutralWhite,
    textInverted: tokens.colors.neutralColors.neutral900,
    // Add other dark theme color overrides
  },
  // The rest of the tokens remain the same
  typography: lightTheme.typography,
  spacing: lightTheme.spacing,
  borders: lightTheme.borders,
  shadows: lightTheme.shadows,
  animations: lightTheme.animations,
};

// Define themes object
const appThemes = {
  light: lightTheme,
  dark: darkTheme,
};

// TypeScript type definitions
type AppBreakpoints = typeof breakpoints;
type AppThemes = typeof appThemes;

// Extend Unistyles types
declare module 'react-native-unistyles' {
  export interface UnistylesThemes extends AppThemes {}
  export interface UnistylesBreakpoints extends AppBreakpoints {}
}

// Configure Unistyles
StyleSheet.configure({
  themes: appThemes,
  breakpoints,
  settings: {
    initialTheme: 'light',
    // adaptiveThemes: true,
  },
});

export { breakpoints, darkTheme, lightTheme };

