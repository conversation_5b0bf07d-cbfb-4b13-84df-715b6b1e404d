/**
 * Main entry point for the design system
 */

// Export tokens
import tokens from './tokens';
export { tokens };

// Export components
    import components from './components';
export { components };

// Export individual components for easier imports
    export * from './components';

// Export theme
import { AppTheme, darkTheme, lightTheme, themes } from './theme';
export { AppTheme, darkTheme, lightTheme, themes };

// Export theme provider and hooks
    import ThemeProvider, { useAppTheme } from './provider';
export { ThemeProvider, useAppTheme };

// Export Unistyles configuration
    import { breakpoints } from './unistyles';
export { breakpoints, darkTheme, lightTheme };

// Default export
export default {
  tokens,
  components,
  ThemeProvider,
  useAppTheme,
};
