/**
 * Theme configuration for our custom design system
 */

import tokens from './tokens';

// Define typography variants
export type TypographyVariant =
  | 'displayLarge'
  | 'displayMedium'
  | 'displaySmall'
  | 'headlineLarge'
  | 'headlineMedium'
  | 'headlineSmall'
  | 'titleLarge'
  | 'titleMedium'
  | 'titleSmall'
  | 'bodyLarge'
  | 'bodyMedium'
  | 'bodySmall'
  | 'labelLarge'
  | 'labelMedium'
  | 'labelSmall';

// Define typography styles
export const typographyStyles = {
  displayLarge: tokens.typography.headingTypography.heading3xl,
  displayMedium: tokens.typography.headingTypography.heading2xl,
  displaySmall: tokens.typography.headingTypography.headingXl,

  headlineLarge: tokens.typography.headingTypography.headingL,
  headlineMedium: tokens.typography.headingTypography.headingM,
  headlineSmall: tokens.typography.headingTypography.headingS,

  titleLarge: tokens.typography.headingTypography.headingXs,
  titleMedium: tokens.typography.headingTypography.heading2xs,
  titleSmall: tokens.typography.headingTypography.heading3xs,

  bodyLarge: tokens.typography.bodyTypography.bodyXL,
  bodyMedium: tokens.typography.bodyTypography.bodyM,
  bodySmall: tokens.typography.bodyTypography.bodyS,

  labelLarge: tokens.typography.bodyTypography.bodyL,
  labelMedium: tokens.typography.bodyTypography.bodyM,
  labelSmall: tokens.typography.bodyTypography.bodyXS,
};

// Create light theme
export const lightTheme = {
  // Colors
  colors: {
    // Primary colors
    primary: tokens.colors.secondaryColors.accentGreen700,
    primaryLight: tokens.colors.secondaryColors.accentGreen600,
    primaryDark: tokens.colors.secondaryColors.accentGreen800,
    primaryContainer: tokens.colors.secondaryColors.accentGreen100,
    onPrimary: tokens.colors.neutralColors.neutralWhite,
    onPrimaryContainer: tokens.colors.secondaryColors.accentGreen800,

    // Secondary colors
    secondary: tokens.colors.brandColors.brandDarkRed,
    secondaryLight: tokens.colors.brandColors.brandRed,
    secondaryDark: tokens.colors.brandSupport.darkerRed,
    secondaryContainer: tokens.colors.secondaryColors.pink100,
    onSecondary: tokens.colors.neutralColors.neutralWhite,
    onSecondaryContainer: tokens.colors.secondaryColors.pink900,

    // Tertiary colors
    tertiary: tokens.colors.secondaryColors.purple700,
    tertiaryLight: tokens.colors.secondaryColors.purple500,
    tertiaryDark: tokens.colors.secondaryColors.purple900,
    tertiaryContainer: tokens.colors.secondaryColors.purple100,
    onTertiary: tokens.colors.neutralColors.neutralWhite,
    onTertiaryContainer: tokens.colors.secondaryColors.purple900,

    // Error colors
    error: tokens.colors.feedbackColors.feedbackError,
    errorContainer: tokens.colors.feedbackColors.feedbackBackgroundError,
    onError: tokens.colors.neutralColors.neutralWhite,
    onErrorContainer: tokens.colors.feedbackColors.feedbackError,

    // Background colors
    background: tokens.colors.backgroundColors.backgroundPrimary,
    onBackground: tokens.colors.textColors.textPrimary,
    surface: tokens.colors.backgroundColors.backgroundPrimary,
    onSurface: tokens.colors.textColors.textPrimary,
    surfaceVariant: tokens.colors.backgroundColors.backgroundSecondary,
    onSurfaceVariant: tokens.colors.textColors.textPrimary,

    // Border colors
    outline: tokens.colors.borderColors.borderDividerMediumEmphasis,
    outlineVariant: tokens.colors.borderColors.borderDividerLowEmphasis,

    // Additional semantic colors
    ...tokens.colors.textColors,
    ...tokens.colors.iconColors,
    ...tokens.colors.borderColors,
    ...tokens.colors.linkColors,
    ...tokens.colors.feedbackColors,

    // Secondary colors needed by components
    green100: tokens.colors.secondaryColors.green100,
    green900: tokens.colors.secondaryColors.green900,
    yellow100: tokens.colors.secondaryColors.yellow100,
    yellow900: tokens.colors.secondaryColors.yellow900,
    pink100: tokens.colors.secondaryColors.pink100,
    pink900: tokens.colors.secondaryColors.pink900,
    neutralWhite: tokens.colors.neutralColors.neutralWhite,
    neutral900: tokens.colors.neutralColors.neutral900,

    // Background colors
    backgroundPrimary: tokens.colors.backgroundColors.backgroundPrimary,
    backgroundSecondary: tokens.colors.backgroundColors.backgroundSecondary,
    backgroundTertiary: tokens.colors.backgroundColors.backgroundTertiary,
    backgroundScrim: tokens.colors.backgroundColors.backgroundScrim,
    backgroundDark: tokens.colors.backgroundColors.backgroundDark,
    backgroundPressed: tokens.colors.backgroundColors.backgroundPressed,
  },

  // Typography
  typography: typographyStyles,

  // Spacing
  spacing: tokens.spacing.space,

  // Borders
  borders: {
    radii: tokens.borders.radii,
    widths: tokens.borders.borderWidths,
    outlines: tokens.borders.outlines,
    components: tokens.borders.componentBorders,
  },

  // Shadows
  shadows: {
    ...tokens.shadows.boxShadows,
    ...tokens.shadows.borderShadows,
    ...tokens.shadows.componentShadows,
  },

  // Animations
  animations: {
    ...tokens.animation.transitions,
    ...tokens.animation.animations,
  },
};

// Create dark theme
export const darkTheme = {
  // Colors
  colors: {
    // Primary colors
    primary: tokens.colors.secondaryColors.accentGreen600,
    primaryLight: tokens.colors.secondaryColors.accentGreen300,
    primaryDark: tokens.colors.secondaryColors.accentGreen700,
    primaryContainer: tokens.colors.secondaryColors.accentGreen800,
    onPrimary: tokens.colors.neutralColors.neutralBlack,
    onPrimaryContainer: tokens.colors.secondaryColors.accentGreen100,

    // Secondary colors
    secondary: tokens.colors.brandColors.brandRed,
    secondaryLight: tokens.colors.brandColors.brandLightRed,
    secondaryDark: tokens.colors.brandColors.brandDarkRed,
    secondaryContainer: tokens.colors.secondaryColors.pink800,
    onSecondary: tokens.colors.neutralColors.neutralBlack,
    onSecondaryContainer: tokens.colors.secondaryColors.pink100,

    // Tertiary colors
    tertiary: tokens.colors.secondaryColors.purple500,
    tertiaryLight: tokens.colors.secondaryColors.purple300,
    tertiaryDark: tokens.colors.secondaryColors.purple700,
    tertiaryContainer: tokens.colors.secondaryColors.purple800,
    onTertiary: tokens.colors.neutralColors.neutralBlack,
    onTertiaryContainer: tokens.colors.secondaryColors.purple100,

    // Error colors
    error: tokens.colors.feedbackColors.feedbackError,
    errorContainer: tokens.colors.secondaryColors.pink800,
    onError: tokens.colors.neutralColors.neutralBlack,
    onErrorContainer: tokens.colors.secondaryColors.pink100,

    // Background colors
    background: tokens.colors.neutralColors.neutral900,
    onBackground: tokens.colors.neutralColors.neutralWhite,
    surface: tokens.colors.neutralColors.neutral900,
    onSurface: tokens.colors.neutralColors.neutralWhite,
    surfaceVariant: tokens.colors.neutralColors.neutral800,
    onSurfaceVariant: tokens.colors.neutralColors.neutralWhite,

    // Border colors
    outline: tokens.colors.neutralColors.neutral400,
    outlineVariant: tokens.colors.neutralColors.neutral800,

    // Additional semantic colors - dark mode overrides
    textPrimary: tokens.colors.neutralColors.neutralWhite,
    textInverted: tokens.colors.neutralColors.neutral900,
    backgroundPrimary: tokens.colors.neutralColors.neutral900,
    backgroundSecondary: tokens.colors.neutralColors.neutral800,
    backgroundTertiary: tokens.colors.neutralColors.neutral800,
    backgroundScrim: tokens.colors.opacityColors.blackOpacity70,
    backgroundDark: tokens.colors.neutralColors.neutral900,
    backgroundPressed: tokens.colors.neutralColors.neutral800,

    // Secondary colors needed by components
    green100: tokens.colors.secondaryColors.green100,
    green900: tokens.colors.secondaryColors.green900,
    yellow100: tokens.colors.secondaryColors.yellow100,
    yellow900: tokens.colors.secondaryColors.yellow900,
    pink100: tokens.colors.secondaryColors.pink100,
    pink900: tokens.colors.secondaryColors.pink900,
    neutralWhite: tokens.colors.neutralColors.neutralWhite,
    neutral900: tokens.colors.neutralColors.neutral900,

    // Feedback colors
    feedbackBackgroundError: tokens.colors.feedbackColors.feedbackBackgroundError,
    feedbackBackgroundSuccess: tokens.colors.feedbackColors.feedbackBackgroundSuccess,
    feedbackBackgroundWarning: tokens.colors.feedbackColors.feedbackBackgroundWarning,
    feedbackBackgroundInfo: tokens.colors.feedbackColors.feedbackBackgroundInfo,

    // Text colors for backgrounds
    textOnBackgroundVarOne: tokens.colors.textColors.textOnBackgroundVarOne,
    textOnBackgroundVarTwo: tokens.colors.textColors.textOnBackgroundVarTwo,
    textOnBackgroundVarThree: tokens.colors.textColors.textOnBackgroundVarThree,
    textOnBackgroundVarFour: tokens.colors.textColors.textOnBackgroundVarFour,
    textOnBackgroundVarFive: tokens.colors.textColors.textOnBackgroundVarFive,
    textOnBackgroundVarSix: tokens.colors.textColors.textOnBackgroundVarSix,
    textBrand: tokens.colors.textColors.textBrand,
  },

  // Typography - same as light theme
  typography: typographyStyles,

  // Spacing - same as light theme
  spacing: lightTheme.spacing,

  // Borders - same as light theme
  borders: lightTheme.borders,

  // Shadows - same as light theme but with darker colors
  shadows: {
    ...tokens.shadows.boxShadows,
    ...tokens.shadows.borderShadows,
    ...tokens.shadows.componentShadows,
  },

  // Animations - same as light theme
  animations: lightTheme.animations,
};

// Export theme type
export type AppTheme = typeof lightTheme | typeof darkTheme;

// Export themes
export const themes = {
  light: lightTheme,
  dark: darkTheme,
};
