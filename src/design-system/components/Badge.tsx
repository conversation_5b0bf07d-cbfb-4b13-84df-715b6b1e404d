/**
 * Badge component for the design system
 * Theme-aware implementation
 */

import React from 'react';
import { TextStyle, View, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';
import { Text } from './Text';

// Badge color variants
export type BadgeColorVariant = 'alpha' | 'success' | 'warning' | 'error' | 'highemphasis';

// Badge size variants
export type BadgeSizeVariant = 'S' | 'M';

// Badge props
export interface BadgeProps {
  /** Color variant of the badge */
  color?: BadgeColorVariant;
  /** Size variant of the badge */
  size?: BadgeSizeVariant;
  /** Content of the badge */
  children: string;
  /** Custom style for the badge */
  style?: ViewStyle;
  /** Custom style for the text */
  textStyle?: TextStyle;
  /** Whether the badge should be visible */
  visible?: boolean;
}

/**
 * Badge component
 *
 * Visual indicator used to highlight an item.
 */
export const Badge: React.FC<BadgeProps> = ({
  color = 'alpha',
  size = 'S',
  children,
  style,
  textStyle,
  visible = true,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  if (!visible) {
    return null;
  }

  // Helper functions for styles
  const getColorStyle = (colorVariant: BadgeColorVariant): ViewStyle => {
    switch (colorVariant) {
      case 'alpha':
        return { backgroundColor: theme.colors.feedbackBackgroundSuccess };
      case 'success':
        return { backgroundColor: theme.colors.green100 };
      case 'warning':
        return { backgroundColor: theme.colors.yellow100 };
      case 'error':
        return { backgroundColor: theme.colors.pink100 };
      case 'highemphasis':
        return { backgroundColor: theme.colors.neutral900 };
      default:
        return { backgroundColor: theme.colors.feedbackBackgroundSuccess };
    }
  };

  const getTextColorStyle = (colorVariant: BadgeColorVariant): TextStyle => {
    switch (colorVariant) {
      case 'alpha':
        return { color: theme.colors.textOnBackgroundVarFive };
      case 'success':
        return { color: theme.colors.green900 };
      case 'warning':
        return { color: theme.colors.yellow900 };
      case 'error':
        return { color: theme.colors.pink900 };
      case 'highemphasis':
        return { color: theme.colors.neutralWhite };
      default:
        return { color: theme.colors.textOnBackgroundVarFive };
    }
  };

  const getSizeStyle = (sizeVariant: BadgeSizeVariant): ViewStyle => {
    switch (sizeVariant) {
      case 'S':
        return {}; // Small size (default)
      case 'M':
        return {}; // Medium size
      default:
        return {}; // Small size
    }
  };

  const getTextSizeStyle = (sizeVariant: BadgeSizeVariant): TextStyle => {
    switch (sizeVariant) {
      case 'S':
        return {
          fontSize: theme.typography.labelSmall.fontSize,
          lineHeight: theme.typography.labelSmall.lineHeight,
        };
      case 'M':
        return {
          fontSize: theme.typography.bodySmall.fontSize,
          lineHeight: theme.typography.bodySmall.lineHeight,
        };
      default:
        return {
          fontSize: theme.typography.labelSmall.fontSize,
          lineHeight: theme.typography.labelSmall.lineHeight,
        };
    }
  };

  // Dynamic styles that depend on theme
  const styles = {
    badge: {
      borderRadius: theme.borders.radii.round,
      paddingHorizontal: theme.spacing[3],
      paddingVertical: theme.spacing[1],
      alignSelf: 'flex-start' as const,
    },
    text: {
      fontFamily: theme.typography.bodyMedium.fontFamily,
      fontWeight: 'bold' as const,
      textAlign: 'center' as const,
    },
  };

  return (
    <View
      style={[
        styles.badge,
        getColorStyle(color),
        getSizeStyle(size),
        style,
      ]}
      accessibilityRole="text"
    >
      <Text
        style={[
          styles.text,
          getTextColorStyle(color),
          getTextSizeStyle(size),
          textStyle,
        ]}
      >
        {children}
      </Text>
    </View>
  );
};

export default Badge;
