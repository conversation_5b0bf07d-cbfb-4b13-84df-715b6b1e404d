/**
 * Heading component for the design system
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import tokens from '../tokens';

// Heading size variants
export type HeadingSize = '3XS' | '2XS' | 'XS' | 'S' | 'M' | 'L' | 'XL' | '2XL' | '3XL';

// Heading level variants
export type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

// Heading props
export interface HeadingProps extends Omit<RNTextProps, 'children'> {
  /** The visual size of the heading */
  size?: HeadingSize;
  /** The semantic level of the heading (for accessibility) */
  as: HeadingLevel;
  /** The color of the heading */
  color?: string;
  /** ID for the heading (for accessibility) */
  id?: string;
  /** Child components */
  children: React.ReactNode;
}

// Heading component
export const Heading: React.FC<HeadingProps> = ({
  size = 'XL',
  as = 'h1',
  color = tokens.colors.textColors.textPrimary,
  id,
  children,
  style,
  ...rest
}) => {
  // Map heading size to typography style
  const getTypographyStyle = (): TextStyle => {
    switch (size) {
      case '3XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['3XL'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[20],
        };
      case '2XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['2XL'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[16],
        };
      case 'XL':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XL,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[12],
        };
      case 'L':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.L,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[10],
        };
      case 'M':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.M,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[9],
        };
      case 'S':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.S,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[7],
        };
      case 'XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XS,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[6],
        };
      case '2XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['2XS'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[6],
        };
      case '3XS':
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes['3XS'],
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[5],
        };
      default:
        return {
          fontFamily: tokens.typography.fontFamilies.heading,
          fontSize: tokens.typography.fontSizes.XL,
          fontWeight: 'bold',
          lineHeight: tokens.typography.lineHeights[12],
        };
    }
  };

  // Map heading level to accessibility role
  const getAccessibilityRole = () => {
    return 'heading';
  };

  // Get heading level as number for accessibility
  const getHeadingLevel = (): number => {
    return parseInt(as.substring(1), 10);
  };

  return (
    <RNText
      style={[
        getTypographyStyle(),
        { color },

        style,
      ]}
      // accessibilityRole={getAccessibilityRole()}
      accessibilityLabel={`Heading level ${getHeadingLevel()}`}
      nativeID={id}
      {...rest}
    >
      {children}
    </RNText>
  );
};

export default Heading;
