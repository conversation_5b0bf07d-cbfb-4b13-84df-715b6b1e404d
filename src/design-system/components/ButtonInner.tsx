/**
 * ButtonInner component for the design system
 */

import React, { FC, ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import tokens from '../tokens';
import { Text } from './Text';

// Check if children contains an icon
const hasIcon = (children: ReactNode): boolean => {
  if (React.Children.count(children) <= 1) return false;

  // In React Native, we'll consider the first child as an icon if there are multiple children
  return React.Children.count(children) > 1;
};

interface ButtonInnerProps {
  children: ReactNode;
  'aria-hidden'?: boolean;
}

export const ButtonInner: FC<ButtonInnerProps> = ({ children, 'aria-hidden': ariaHidden }) => {
  return (
    <View
      style={styles.buttonInner}
      accessibilityElementsHidden={ariaHidden}
      importantForAccessibility={ariaHidden ? 'no-hide-descendants' : 'auto'}
    >
      {children}
      {!hasIcon(children) && (
        <View style={styles.chevronIconWrapper}>
          <Text style={styles.chevron}>
            {'\u203A'} {/* Unicode for › character */}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonInner: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: tokens.spacing.space[3],
    // fontWeight should be in Text style, not View style
  },
  chevronIconWrapper: {
    // This will be conditionally displayed based on button variant
  },
  chevron: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default ButtonInner;
