/**
 * Stack component for the design system
 */

import React from 'react';
import { StyleSheet, View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Stack direction values
export type StackDirection = 'row' | 'column';

// Stack alignment values
export type StackAlignment = 'start' | 'center' | 'end' | 'justify';

// Stack gap values
export type StackGap = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '10' | '12' | '16';

// Stack props
export interface StackProps extends ViewProps {
  /** Direction of the stack */
  direction?: StackDirection | Record<string, StackDirection>;
  /** Gap between stack items */
  gap?: StackGap | Record<string, StackGap>;
  /** Whether the stack should wrap */
  wrap?: boolean;
  /** Horizontal alignment of stack items */
  alignX?: StackAlignment;
  /** Vertical alignment of stack items */
  alignY?: StackAlignment;
  /** Whether the stack should be displayed inline */
  inline?: boolean;
  /** Child components */
  children: React.ReactNode;
}

// Stack item props
export interface StackItemProps extends ViewProps {
  /** Whether the item should grow to fill available space */
  grow?: boolean | Record<string, boolean>;
  /** Whether the item should shrink to fit available space */
  shrink?: boolean | Record<string, boolean>;
  /** Child components */
  children: React.ReactNode;
}

// Stack styles
const styles = StyleSheet.create({
  stack: {
    display: 'flex',
    flexWrap: 'nowrap',
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  wrap: {
    flexWrap: 'wrap',
  },
  noWrap: {
    flexWrap: 'nowrap',
  },
  inline: {
    // React Native doesn't support inline-flex, so we use flex
    display: 'flex',
  },
  stackItem: {
    flexGrow: 0,
  },
  grow: {
    flexGrow: 1,
    flexBasis: 0,
  },
  noGrow: {
    flexGrow: 0,
  },
  shrink: {
    flexShrink: 1,
  },
  noShrink: {
    flexShrink: 0,
  },
});

// StackItem component
const StackItem: React.FC<StackItemProps> = ({
  grow = false,
  shrink = true,
  style,
  children,
  ...rest
}) => {
  // Get grow style
  const getGrowStyle = (): ViewStyle => {
    if (typeof grow === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return grow.initial ? styles.grow : styles.noGrow;
    }
    return grow ? styles.grow : styles.noGrow;
  };

  // Get shrink style
  const getShrinkStyle = (): ViewStyle => {
    if (typeof shrink === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return shrink.initial ? styles.shrink : styles.noShrink;
    }
    return shrink ? styles.shrink : styles.noShrink;
  };

  return (
    <View style={[styles.stackItem, getGrowStyle(), getShrinkStyle(), style]} {...rest}>
      {children}
    </View>
  );
};

// Stack component
const StackComponent: React.FC<StackProps> = ({
  direction = 'column',
  gap = '0',
  wrap = false,
  alignX,
  alignY,
  inline,
  style,
  children,
  ...rest
}) => {
  // Get direction style
  const getDirectionStyle = (): ViewStyle => {
    if (typeof direction === 'object') {
      // For simplicity, we'll just use the 'initial' value
      return direction.initial === 'row' ? styles.row : styles.column;
    }
    return direction === 'row' ? styles.row : styles.column;
  };

  // Get gap value
  const getGapValue = (): number => {
    const gapValue = typeof gap === 'object' ? gap.initial : gap;
    return gapValue ? tokens.spacing.space[gapValue] : 0;
  };

  // Get wrap style
  const getWrapStyle = (): ViewStyle => {
    return wrap ? styles.wrap : styles.noWrap;
  };

  // Get alignment styles
  const getAlignmentStyles = (): ViewStyle => {
    const isRow = typeof direction === 'object'
      ? direction.initial === 'row'
      : direction === 'row';

    const alignmentStyle: ViewStyle = {};

    // Handle alignX
    if (alignX) {
      if (isRow) {
        // For row direction, alignX affects justifyContent
        switch (alignX) {
          case 'start': alignmentStyle.justifyContent = 'flex-start'; break;
          case 'center': alignmentStyle.justifyContent = 'center'; break;
          case 'end': alignmentStyle.justifyContent = 'flex-end'; break;
          case 'justify': alignmentStyle.justifyContent = 'space-between'; break;
        }
      } else {
        // For column direction, alignX affects alignItems
        switch (alignX) {
          case 'start': alignmentStyle.alignItems = 'flex-start'; break;
          case 'center': alignmentStyle.alignItems = 'center'; break;
          case 'end': alignmentStyle.alignItems = 'flex-end'; break;
          case 'justify': alignmentStyle.alignItems = 'stretch'; break;
        }
      }
    }

    // Handle alignY
    if (alignY) {
      if (isRow) {
        // For row direction, alignY affects alignItems
        switch (alignY) {
          case 'start': alignmentStyle.alignItems = 'flex-start'; break;
          case 'center': alignmentStyle.alignItems = 'center'; break;
          case 'end': alignmentStyle.alignItems = 'flex-end'; break;
          case 'justify': alignmentStyle.alignItems = 'stretch'; break;
        }
      } else {
        // For column direction, alignY affects justifyContent
        switch (alignY) {
          case 'start': alignmentStyle.justifyContent = 'flex-start'; break;
          case 'center': alignmentStyle.justifyContent = 'center'; break;
          case 'end': alignmentStyle.justifyContent = 'flex-end'; break;
          case 'justify': alignmentStyle.justifyContent = 'space-between'; break;
        }
      }
    }

    return alignmentStyle;
  };

  return (
    <View
      style={[
        styles.stack,
        getDirectionStyle(),
        getWrapStyle(),
        { gap: getGapValue() },
        getAlignmentStyles(),
        inline && styles.inline,
        style,
      ]}
      {...rest}
    >
      {children}
    </View>
  );
};

// Add StackItem to Stack component
type StackComponentType = typeof StackComponent & {
  Item: typeof StackItem;
};

// Create Stack with StackItem
const Stack = StackComponent as StackComponentType;
Stack.Item = StackItem;

// Display names
StackComponent.displayName = 'Stack';
StackItem.displayName = 'Stack.Item';

export default Stack;
