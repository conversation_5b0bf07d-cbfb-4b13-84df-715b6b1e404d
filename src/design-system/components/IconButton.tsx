/**
 * IconButton component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Pressable,
  StyleSheet,
  TouchableOpacityProps,
  ViewStyle
} from 'react-native';
import tokens from '../tokens';
import VisuallyHidden from './VisuallyHidden';

// IconButton color variants
export type IconButtonColor = 'primary' | 'secondary' | 'inverted';

// IconButton size variants
export type IconButtonSize = 'regular' | 'large';

// IconButton props
export interface IconButtonProps extends TouchableOpacityProps {
  /** Accessible label for the button */
  label: string;
  /** URL to navigate to when pressed (makes it behave like a link) */
  href?: string;
  /** Whether the button is disabled */
  isDisabled?: boolean;
  /** Color variant of the button */
  color?: IconButtonColor;
  /** Size variant of the button */
  size?: IconButtonSize;
  /** Whether this is the current active item */
  isCurrent?: boolean;
  /** Function to call when the button is pressed */
  onPress?: (event: GestureResponderEvent) => void;
  /** Child components (should be an icon) */
  children: React.ReactNode;
}

// IconButton styles
const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderRadius: tokens.borders.radii.round,
  },
  regular: {
    minWidth: tokens.spacing.sizes.targetMinWidth,
    minHeight: tokens.spacing.sizes.targetMinHeight,
  },
  large: {
    minWidth: 48,
    minHeight: 48,
  },
  disabled: {
    opacity: 0.5,
  },
  primary: {
    // Default style
  },
  secondary: {
    // Secondary style
  },
  inverted: {
    // Inverted style
  },
  current: {
    // Current style
  },
  pressedPrimary: {
    backgroundColor: tokens.colors.neutralColors.neutral300,
  },
  pressedInverted: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  hoverPrimary: {
    backgroundColor: tokens.colors.backgroundColors.backgroundTertiary,
  },
  hoverInverted: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
});

// IconButton component
export const IconButton: React.FC<IconButtonProps> = ({
  label,
  href,
  isDisabled = false,
  color = 'primary',
  size = 'regular',
  isCurrent,
  onPress,
  style,
  children,
  ...rest
}) => {
  // Get size style
  const getSizeStyle = (): ViewStyle => {
    return size === 'large' ? styles.large : styles.regular;
  };

  // Get color style
  const getColorStyle = (): ViewStyle => {
    switch (color) {
      case 'secondary':
        return styles.secondary;
      case 'inverted':
        return styles.inverted;
      default:
        return styles.primary;
    }
  };

  // Get pressed style
  const getPressedStyle = (): ViewStyle => {
    return color === 'inverted' ? styles.pressedInverted : styles.pressedPrimary;
  };

  // Get hover style (for web)
  const getHoverStyle = (): ViewStyle => {
    return color === 'inverted' ? styles.hoverInverted : styles.hoverPrimary;
  };

  // Render the button
  return (
    <Pressable
      disabled={isDisabled}
      onPress={onPress}
      accessibilityLabel={label}
      accessibilityRole={href ? 'link' : 'button'}
      accessibilityState={{
        disabled: isDisabled,
        selected: isCurrent,
      }}
      style={({ pressed }) => [
        styles.button,
        getSizeStyle(),
        getColorStyle(),
        isDisabled && styles.disabled,
        isCurrent && styles.current,
        pressed && getPressedStyle(),
        style,
      ]}
      {...rest}
    >
      <VisuallyHidden>{label}</VisuallyHidden>
      {children}
    </Pressable>
  );
};

export default IconButton;
