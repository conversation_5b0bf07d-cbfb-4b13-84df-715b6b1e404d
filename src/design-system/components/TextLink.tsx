/**
 * TextLink component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Linking,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View
} from 'react-native';
import tokens from '../tokens';
import { Text } from './Text';

// TextLink emphasis variants
export type TextLinkEmphasis = 'low' | 'medium' | 'high';

// TextLink color types
export type TextLinkColor = string;

// TextLink props
export interface TextLinkProps {
  /** Text shown inside the link */
  children: React.ReactNode;
  /** Emphasis level of the link */
  emphasis?: TextLinkEmphasis;
  /** URL to navigate to when pressed */
  href?: string;
  /** Icon shown to the left of the text */
  leftIcon?: React.ReactNode;
  /** Icon shown to the right of the text */
  rightIcon?: React.ReactNode;
  /** Function to call when the link is pressed */
  onPress?: (event: GestureResponderEvent) => void;
  /** Target for the link (e.g., '_blank') */
  target?: string;
  /** Color of the link */
  color?: TextLinkColor;
  /** Accessible label for the link */
  ariaLabel?: string;
  /** Custom style for the link */
  style?: StyleProp<TextStyle>;
}

// TextLink styles
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    textDecorationLine: 'underline',
  },
  lowEmphasis: {
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: 'currentColor',
  },
  mediumEmphasis: {
    fontWeight: 'bold',
    color: tokens.colors.linkColors.linkPrimary,
  },
  highEmphasis: {
    fontWeight: 'bold',
    color: tokens.colors.linkColors.linkBrand,
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: tokens.colors.linkColors.linkBrand,
  },
  leftIconContainer: {
    marginRight: tokens.spacing.space[1],
  },
  rightIconContainer: {
    marginLeft: tokens.spacing.space[1],
  },
});

// TextLink component
export const TextLink: React.FC<TextLinkProps> = ({
  children,
  emphasis = 'low',
  href,
  leftIcon,
  rightIcon,
  onPress,
  target,
  color,
  ariaLabel,
  style,
}) => {
  // Handle press event
  const handlePress = (event: GestureResponderEvent) => {
    if (onPress) {
      onPress(event);
    } else if (href) {
      Linking.openURL(href);
    }
  };

  // Get emphasis style
  const getEmphasisStyle = (): TextStyle => {
    switch (emphasis) {
      case 'medium':
        return styles.mediumEmphasis;
      case 'high':
        return styles.highEmphasis;
      case 'low':
      default:
        return styles.lowEmphasis;
    }
  };

  // Get color style
  const getColorStyle = (): TextStyle => {
    if (!color) return {};
    
    return { color };
  };

  // Render the link content
  const renderLinkContent = () => {
    return (
      <View style={styles.container}>
        {leftIcon && <View style={styles.leftIconContainer}>{leftIcon}</View>}
        <Text
          style={[
            styles.text,
            getEmphasisStyle(),
            getColorStyle(),
            style,
          ]}
        >
          {children}
        </Text>
        {rightIcon && <View style={styles.rightIconContainer}>{rightIcon}</View>}
      </View>
    );
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      accessibilityRole="link"
      accessibilityLabel={ariaLabel || (typeof children === 'string' ? children : undefined)}
      accessibilityHint={target === '_blank' ? 'Opens in a new window' : undefined}
    >
      {renderLinkContent()}
    </TouchableOpacity>
  );
};

export default TextLink;
