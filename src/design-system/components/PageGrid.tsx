/**
 * PageGrid component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';

// Grid data (source of truth for the PageGrid)
export const gridData = {
  columns: {
    initial: 9,
    md: 12,
  },
  gaps: {
    initial: tokens.spacing.space[2],
    md: tokens.spacing.space[3],
    lg: tokens.spacing.space[4],
    xl: tokens.spacing.space[6],
  },
  gutters: {
    initial: tokens.spacing.space[6],
    md: tokens.spacing.space[10],
    xl: tokens.spacing.space[24],
  },
  maxWidth: {
    initial: '100%',
    xl: 1440,
  },
};

// PageGrid props
export interface PageGridProps extends ViewProps {
  /** Grid template areas */
  gridTemplateAreas?: string | Record<string, string>;
  /** Grid template rows */
  gridTemplateRows?: string;
  /** Child components */
  children: React.ReactNode;
}

// PageGrid item props
export interface PageGridItemProps extends ViewProps {
  /** Grid area name */
  gridArea?: string;
  /** Grid column start */
  gridColumnStart?: string | number;
  /** Grid column end */
  gridColumnEnd?: string | number;
  /** Grid column span */
  gridColumn?: string | Record<string, string>;
  /** Grid row start */
  gridRowStart?: string | number;
  /** Grid row end */
  gridRowEnd?: string | number;
  /** Grid row span */
  gridRow?: string;
  /** Order of the grid item */
  order?: number;
  /** Child components */
  children: React.ReactNode;
}

// PageGrid styles
const styles = StyleSheet.create({
  pageGrid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingHorizontal: gridData.gutters.initial,
  },
  pageGridItem: {
    flexGrow: 0,
    flexShrink: 0,
  },
});

// PageGridItem component
const PageGridItem: React.FC<PageGridItemProps> = ({
  gridArea,
  gridColumnStart,
  gridColumnEnd,
  gridColumn,
  gridRowStart,
  gridRowEnd,
  gridRow,
  order,
  style,
  children,
  ...rest
}) => {
  // Parse grid column value
  const parseGridColumn = (): ViewStyle => {
    if (!gridColumn) return {};

    // Handle responsive gridColumn
    if (typeof gridColumn === 'object') {
      // For simplicity, we'll just use the 'initial' value
      const initialValue = gridColumn.initial;
      if (initialValue) {
        return parseGridColumnString(initialValue);
      }
      return {};
    }

    return parseGridColumnString(gridColumn);
  };

  // Parse grid column string
  const parseGridColumnString = (columnStr: string): ViewStyle => {
    // Handle full width
    if (columnStr === '1/-1' || columnStr === '1 / -1') {
      return { width: '100%' };
    }

    // Handle specific column spans
    const parts = columnStr.split('/').map(p => p.trim());
    if (parts.length === 2) {
      const start = parseInt(parts[0], 10);
      const end = parseInt(parts[1], 10);

      if (!isNaN(start) && !isNaN(end)) {
        const totalColumns = gridData.columns.initial;
        const span = end > 0 ? end - start : totalColumns + end - start;
        return { width: `${(span / totalColumns) * 100}%` };
      }
    }

    return {};
  };

  // Get column span from start/end
  const getColumnSpan = (): ViewStyle => {
    if (gridColumnStart && gridColumnEnd) {
      const start = parseInt(String(gridColumnStart), 10);
      const end = parseInt(String(gridColumnEnd), 10);

      if (!isNaN(start) && !isNaN(end)) {
        const totalColumns = gridData.columns.initial;
        const span = end > 0 ? end - start : totalColumns + end - start;
        return { width: `${(span / totalColumns) * 100}%` };
      }
    }

    return {};
  };

  // Create style object
  const itemStyle: ViewStyle = {
    ...(order !== undefined && { order }),
    ...parseGridColumn(),
    ...getColumnSpan(),
  };

  return (
    <View style={[styles.pageGridItem, itemStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// PageGrid component
const PageGridComponent: React.FC<PageGridProps> = ({
  gridTemplateAreas,
  gridTemplateRows,
  style,
  children,
  ...rest
}) => {
  // Get gap based on screen width
  const getGap = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate gap value
    return gridData.gaps.initial;
  };

  // Get gutters based on screen width
  const getGutters = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate gutter value
    return gridData.gutters.initial;
  };

  // Get max width based on screen width
  const getMaxWidth = (): number => {
    // In a real implementation, this would use a hook to get the current screen width
    // and return the appropriate max width value
    const maxWidth = gridData.maxWidth.initial;
    // Convert string percentage to number if needed
    if (typeof maxWidth === 'string' && maxWidth.endsWith('%')) {
      return parseFloat(maxWidth) / 100;
    }
    return typeof maxWidth === 'number' ? maxWidth : 1;
  };

  // Create style object
  const gridStyle: ViewStyle = {
    gap: getGap(),
    paddingHorizontal: getGutters(),
    maxWidth: getMaxWidth(),
  };

  return (
    <View style={[styles.pageGrid, gridStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Add PageGridItem to PageGrid component
type PageGridComponentType = typeof PageGridComponent & {
  Item: typeof PageGridItem;
};

// Create PageGrid with PageGridItem
const PageGrid = PageGridComponent as PageGridComponentType;
PageGrid.Item = PageGridItem;

// Display names
PageGridComponent.displayName = 'PageGrid';
PageGridItem.displayName = 'PageGrid.Item';

export default PageGrid;
