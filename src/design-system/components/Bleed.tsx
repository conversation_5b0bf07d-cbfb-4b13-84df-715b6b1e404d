/**
 * Bleed component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Spacing values
type SpacingValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '8' | '10' | '12' | '16' | '24' | '32';

// Special horizontal value for grid gutters
type HorizontalValue = SpacingValue | 'gridGutter';

// Bleed props
export interface BleedProps extends ViewProps {
  /** Adds negative margins to the left and right of the child */
  horizontal?: HorizontalValue;
  /** Adds negative margins to the top and bottom of the child */
  vertical?: SpacingValue;
  /** Adds negative margins to the top of the child */
  top?: SpacingValue;
  /** Adds negative margins to the bottom of the child */
  bottom?: SpacingValue;
  /** Adds negative margins to the left of the child */
  left?: SpacingValue;
  /** Adds negative margins to the right of the child */
  right?: SpacingValue;
  /** Child components */
  children: React.ReactNode;
}

// Bleed component
export const Bleed: React.FC<BleedProps> = ({
  horizontal,
  vertical,
  top,
  bottom,
  left,
  right,
  style,
  children,
  ...rest
}) => {
  // Get negative margin value based on spacing token
  const getNegativeMargin = (value: SpacingValue | undefined): number | undefined => {
    if (!value) return undefined;
    return -tokens.spacing.space[value];
  };

  // Handle special gridGutter case
  const getHorizontalMargin = (): ViewStyle | undefined => {
    if (horizontal === 'gridGutter') {
      // In React Native, we'll use a simpler approach for the gridGutter
      // since we don't have the same CSS media queries
      return {
        marginHorizontal: -16, // Default gutter size
      };
    }
    
    if (horizontal) {
      return {
        marginHorizontal: getNegativeMargin(horizontal as SpacingValue),
      };
    }
    
    return undefined;
  };

  // Create style object with all margins
  const bleedStyle: ViewStyle = {
    ...(vertical && { marginVertical: getNegativeMargin(vertical) }),
    ...(top && { marginTop: getNegativeMargin(top) }),
    ...(bottom && { marginBottom: getNegativeMargin(bottom) }),
    ...(left && { marginLeft: getNegativeMargin(left) }),
    ...(right && { marginRight: getNegativeMargin(right) }),
    ...getHorizontalMargin(),
  };

  return (
    <View style={[bleedStyle, style]} {...rest}>
      {children}
    </View>
  );
};

export default Bleed;
