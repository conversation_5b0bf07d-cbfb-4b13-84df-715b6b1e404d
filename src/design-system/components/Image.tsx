/**
 * Image component for the design system
 */

import React, { useEffect, useState } from 'react';
import {
  ImageStyle,
  Image as RNImage,
  ImageProps as RNImageProps,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import tokens from '../tokens';

// Object fit values
type ObjectFitValue = 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';

// Object position values
type ObjectPositionValue = 'bottom' | 'center' | 'left' | 'right' | 'top';

// Image props
export interface ImageProps extends Omit<RNImageProps, 'source'> {
  /** Alternative text for the image */
  alt: string;
  /** Aspect ratio of the image (e.g., '16/9') */
  aspectRatio?: string;
  /** Border radius of the image */
  borderRadius?: number;
  /** Whether to lazy load the image */
  hasLazyLoad?: boolean;
  /** Height of the image */
  height?: number;
  /** Maximum height of the image */
  maxHeight?: number;
  /** Maximum width of the image */
  maxWidth?: number;
  /** Minimum height of the image */
  minHeight?: number;
  /** Minimum width of the image */
  minWidth?: number;
  /** How the image should fit within its container */
  objectFit?: ObjectFitValue;
  /** Position of the image within its container */
  objectPosition?: ObjectPositionValue;
  /** Source URL of the image */
  src: string;
  /** Fallback source URL if the main source fails to load */
  fallbackSrc?: string;
  /** Width of the image */
  width?: number;
  /** Custom style for the image */
  style?: ImageStyle;
}

// Image styles
const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  rounded: {
    borderRadius: tokens.borders.radii.m,
  },
  square: {
    borderRadius: 0,
  },
});

// Helper function to get aspect ratio dimensions
const getAspectRatioDimensions = (
  aspectRatio?: string
): { width: number; height: number } | undefined => {
  if (!aspectRatio) return undefined;

  const [widthStr, heightStr] = aspectRatio.split('/');
  const width = parseFloat(widthStr);
  const height = parseFloat(heightStr);

  if (isNaN(width) || isNaN(height)) return undefined;

  return { width, height };
};

// Image component
export const Image: React.FC<ImageProps> = ({
  alt,
  aspectRatio,
  borderRadius = 'square',
  hasLazyLoad,
  height,
  maxHeight,
  maxWidth,
  minHeight,
  minWidth,
  objectFit = 'cover',
  objectPosition,
  src,
  fallbackSrc,
  width,
  style,
  ...rest
}) => {
  const [hasError, setHasError] = useState(false);
  const [imageSource, setImageSource] = useState({ uri: src });

  // Reset error state when src changes
  useEffect(() => {
    setHasError(false);
    setImageSource({ uri: src });
  }, [src]);

  // Handle image load error
  const handleError = () => {
    if (fallbackSrc && !hasError) {
      setHasError(true);
      setImageSource({ uri: fallbackSrc });
    }
  };

  // Get border radius style
  const getBorderRadiusStyle = (): ViewStyle => {
    if (borderRadius !== undefined) {
      return { borderRadius };
    }
    return {};
  };

  // Get object fit as resizeMode prop
  const getResizeMode = (): 'contain' | 'cover' | 'stretch' | 'center' => {
    switch (objectFit) {
      case 'contain':
        return 'contain';
      case 'cover':
        return 'cover';
      case 'fill':
        return 'stretch';
      case 'none':
        return 'center';
      case 'scale-down':
        return 'contain';
      default:
        return 'cover';
    }
  };

  // Get dimensions style
  const getDimensionsStyle = (): ViewStyle => {
    const dimensionsStyle: ViewStyle = {};

    if (width !== undefined) dimensionsStyle.width = width;
    if (height !== undefined) dimensionsStyle.height = height;
    if (minWidth !== undefined) dimensionsStyle.minWidth = minWidth;
    if (minHeight !== undefined) dimensionsStyle.minHeight = minHeight;
    if (maxWidth !== undefined) dimensionsStyle.maxWidth = maxWidth;
    if (maxHeight !== undefined) dimensionsStyle.maxHeight = maxHeight;

    // Handle aspect ratio
    const aspectRatioDimensions = getAspectRatioDimensions(aspectRatio);
    if (aspectRatioDimensions) {
      dimensionsStyle.aspectRatio = aspectRatioDimensions.width / aspectRatioDimensions.height;
    }

    return dimensionsStyle;
  };

  return (
    <View style={[styles.container, getBorderRadiusStyle(), getDimensionsStyle()]}>
      <RNImage
        source={imageSource}
        accessibilityLabel={alt}
        onError={handleError}
        resizeMode={getResizeMode()}
        style={[styles.image, style]}
        {...rest}
      />
    </View>
  );
};

export default Image;
