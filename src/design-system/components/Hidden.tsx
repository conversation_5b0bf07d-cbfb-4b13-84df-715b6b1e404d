/**
 * Hidden component for the design system
 */

import React from 'react';
import { View, ViewProps, useWindowDimensions } from 'react-native';
import { breakpoints } from '../unistyles';

// Breakpoint values
type BreakpointValue = 'sm' | 'md' | 'lg' | 'xl';

// Hidden props
export interface HiddenProps extends ViewProps {
  /** Hide children above the passed breakpoint */
  above?: BreakpointValue;
  /** Hide children below the passed breakpoint */
  below?: BreakpointValue;
  /** Child components */
  children: React.ReactNode;
}

// Hidden component
export const Hidden: React.FC<HiddenProps> = ({
  above,
  below,
  children,
  style,
  ...rest
}) => {
  const { width } = useWindowDimensions();

  // Check if component should be hidden based on breakpoints
  const isHidden = (): boolean => {
    if (above) {
      const breakpoint = breakpoints[above];
      return width >= breakpoint;
    }

    if (below) {
      const breakpoint = breakpoints[below];
      return width < breakpoint;
    }

    return false;
  };

  // If hidden, don't render anything
  if (isHidden()) {
    return null;
  }

  // If the child is a React element, return it directly
  if (React.isValidElement(children)) {
    return children;
  }

  // Otherwise, wrap in a View
  return (
    <View style={style} {...rest}>
      {children}
    </View>
  );
};

export default Hidden;
