/**
 * Button component for the design system
 * Theme-aware implementation
 */

import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  GestureResponderEvent,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from "react-native";
import { StyleSheet } from "react-native-unistyles";
import { useAppTheme } from "../provider";
import tokens from "../tokens";
import { Text } from "./Text";

// Button action variants
export type ButtonAction = "primary" | "secondary" | "primaryRebrand";

// Button sizes
export type ButtonSize = "compact" | "regular";

// Button tone
export type ButtonTone = "onLight" | "onColor";

// Button props
export interface ButtonProps extends Omit<TouchableOpacityProps, "children"> {
  /** Emphasises the importance of your action with colors */
  action?: ButtonAction;
  /** The size of the Button */
  size?: ButtonSize;
  /** Changes the tone based on the background color. Note: This **only** has an effect on **primary** action buttons. */
  tone?: ButtonTone;
  /** Signals the user has to wait before they can press the button. While this is true, the button is disabled. */
  isLoading?: boolean;
  /** Hidden helper text for screenreaders when the button is in the loading state */
  loadingText?: string;
  /** Hidden helper text for screenreaders when the button is no longer in the loading state */
  finishedLoadingText?: string;
  /** Button content */
  children: React.ReactNode;
  /** Optional icon to display before the text */
  icon?: React.ReactNode;
  /** Optional style for the button content */
  contentStyle?: StyleProp<ViewStyle>;
  /** Optional style for the button text */
  labelStyle?: StyleProp<TextStyle>;
  /** Optional button color override */
  buttonColor?: string;
  /** Optional text color override */
  textColor?: string;
  /** Optional onPress handler */
  onPress?: (e: GestureResponderEvent) => void;
}

// Button component
export const Button: React.FC<ButtonProps> = ({
  action = "primary",
  size = "regular",
  tone = "onLight",
  isLoading = false,
  loadingText,
  finishedLoadingText,
  disabled = false,
  style,
  contentStyle,
  labelStyle,
  buttonColor,
  textColor,
  icon,
  children,
  onPress,
  ...rest
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();
  const [helperText, setHelperText] = useState("");

  // Handle loading state and accessibility text
  useEffect(() => {
    if (!isLoading && helperText !== "") {
      setHelperText(finishedLoadingText ?? "Finished loading");
    }
    if (isLoading) {
      setHelperText(loadingText ?? "Loading");
    }
  }, [isLoading, helperText, loadingText, finishedLoadingText]);

  // Get border radius based on action
  const getBorderRadius = () => {
    if (action === "primaryRebrand") {
      return theme.borders.components.button.borderRadiusRebrand;
    }
    return theme.borders.components.button.borderRadiusDefault;
  };

  // Get button background color based on action
  const getButtonBackgroundColor = () => {
    if (action === "primary" && tone === "onColor") {
      return theme.colors.backgroundPrimary;
    }
    if (action === "primary" || action === "primaryRebrand") {
      return theme.colors.primary;
    }
    return "transparent"; // For secondary buttons
  };

  // Get button border color and width
  const getButtonBorder = () => {
    if (action === "secondary") {
      return {
        borderWidth: theme.borders.widths.m,
        borderColor: theme.colors.primary,
      };
    }
    return {};
  };

  // Get text color based on action
  const getTextColor = () => {
    if (action === "primary" && tone === "onColor") {
      return theme.colors.textBrand;
    }
    if (action === "secondary") {
      return theme.colors.textBrand;
    }
    if (action === "primary" || action === "primaryRebrand") {
      return theme.colors.onPrimary;
    }
    return theme.colors.textPrimary;
  };

  // Get size styles
  const getSizeStyles = () => {
    return size === "compact" ? styles.compactContent : styles.regularContent;
  };

  // Determine if we should show a chevron
  const shouldShowChevron = () => {
    return action === "primary" && size === "regular" && !isLoading;
  };

  // Create a custom loading indicator that matches the original design
  const renderLoadingIndicator = () => {
    if (!isLoading) return null;

    const color =
      action === "primary"
        ? theme.colors.onPrimary
        : theme.colors.textBrand;

    return (
      <ActivityIndicator
        size="small"
        color={color}
        style={styles.loadingIndicator}
      />
    );
  };

  // Render the button inner content
  const renderInnerContent = () => {
    return (
      <View style={[styles.buttonInner, getSizeStyles(), contentStyle]}>
        {icon && <View>{icon}</View>}
        <Text
          style={[
            styles.buttonText,
            { color: textColor || getTextColor() },
            labelStyle,
          ]}
        >
          {children}
        </Text>
        {shouldShowChevron() && (
          <Text style={[styles.chevron, { color: getTextColor() }]}>
            {'\u203A'} {/* Unicode for › character */}
          </Text>
        )}
      </View>
    );
  };

  // Get loading background color
  const getLoadingBackgroundColor = () => {
    return isLoading ? theme.colors.secondary : undefined;
  };

  // Dynamic styles that depend on theme
  const buttonStyles = {
    button: {
      alignItems: "center" as const,
      justifyContent: "center" as const,
      minWidth: theme.spacing[10],
      position: "relative" as const,
      overflow: "hidden" as const,
    },
    buttonInner: {
      alignItems: "center" as const,
      flexDirection: "row" as const,
      justifyContent: "center" as const,
      gap: theme.spacing[3],
    },
    buttonText: {
      fontFamily: tokens.typography.fontFamilies.base,
      fontWeight: "700" as const,
    },
    compactContent: {
      paddingVertical: theme.spacing[3],
      paddingHorizontal: theme.spacing[4],
    },
    regularContent: {
      paddingVertical: theme.spacing[4],
      paddingHorizontal: theme.spacing[4],
    },
    chevron: {
      fontSize: 20,
      fontWeight: "700" as const,
      marginLeft: theme.spacing[2],
    },
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      disabled={disabled || isLoading}
      onPress={onPress}
      style={[
        buttonStyles.button,
        {
          backgroundColor: buttonColor || getButtonBackgroundColor(),
          borderRadius: getBorderRadius(),
          ...getButtonBorder(),
        },
        isLoading && { backgroundColor: getLoadingBackgroundColor() },
        (disabled || isLoading) && styles.disabledButton,
        style,
      ]}
      accessibilityState={{
        disabled: disabled || isLoading,
        busy: isLoading,
      }}
      accessibilityLabel={isLoading ? helperText : undefined}
      {...rest}
    >
      {renderInnerContent()}
      {renderLoadingIndicator()}
    </TouchableOpacity>
  );
};

// Static styles that don't depend on theme
const styles = StyleSheet.create({
  disabledButton: {
    opacity: 0.5,
  },
  loadingIndicator: {
    position: "absolute",
  },
  compactButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  regularButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  compactContent: {
    paddingVertical: tokens.spacing.space[3],
    paddingHorizontal: tokens.spacing.space[4],
  },
  regularContent: {
    paddingVertical: tokens.spacing.space[4],
    paddingHorizontal: tokens.spacing.space[4],
  },
  buttonInner: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: tokens.spacing.space[3],
  },
  buttonText: {
    fontFamily: tokens.typography.fontFamilies.base,
    fontWeight: "700",
  },
  chevron: {
    fontSize: 20,
    fontWeight: "700",
    marginLeft: tokens.spacing.space[2],
  },
});

export default Button;
