/**
 * Text component for the design system
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import { useAppTheme } from '../provider';
import { TypographyVariant } from '../theme';

// Text props
export interface TextProps extends RNTextProps {
  variant?: TypographyVariant;
  children: React.ReactNode;
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
}



// Text component
export const Text: React.FC<TextProps> = ({
  variant = 'bodyMedium',
  children,
  color,
  align,
  style,
  ...rest
}) => {
  const { theme } = useAppTheme();

  // Get typography style for the variant
  const variantStyle = theme.typography[variant] as TextStyle;

  // Determine the text color with proper priority
  const textColor = color || theme.colors.textPrimary;

  return (
    <RNText
      style={[
        variantStyle,
        { color: textColor }, // Apply determined color
        align && { textAlign: align },
        style, // Allow style prop to override everything except color
      ]}
      {...rest}
    >
      {children}
    </RNText>
  );
};

// Heading component
export interface HeadingProps extends Omit<TextProps, 'variant'> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export const Heading: React.FC<HeadingProps> = ({
  level = 1,
  children,
  ...rest
}) => {
  // Map heading level to variant
  const getVariant = (): TypographyVariant => {
    switch (level) {
      case 1:
        return 'displayLarge';
      case 2:
        return 'displayMedium';
      case 3:
        return 'displaySmall';
      case 4:
        return 'headlineLarge';
      case 5:
        return 'headlineMedium';
      case 6:
        return 'headlineSmall';
      default:
        return 'displayLarge';
    }
  };

  return (
    <Text variant={getVariant()} {...rest}>
      {children}
    </Text>
  );
};

// Paragraph component
export const Paragraph: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="bodyMedium" {...props} />;
};

// Caption component
export const Caption: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="bodySmall" {...props} />;
};

// Label component
export const Label: React.FC<Omit<TextProps, 'variant'>> = (props) => {
  return <Text variant="labelMedium" {...props} />;
};

// Export all components
export default {
  Text,
  Heading,
  Paragraph,
  Caption,
  Label,
};
