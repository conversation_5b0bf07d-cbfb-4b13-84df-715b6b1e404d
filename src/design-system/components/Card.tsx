/**
 * Card component for the design system
 * Theme-aware implementation
 */

import React, { ReactNode } from 'react';
import { Image, ImageSourcePropType, ImageStyle, StyleProp, View, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';
import tokens from '../tokens';
import { Box } from './Box';
import { Heading, Text } from './Text';

// Card corner variants
export type CardCorners = 'square' | 'rounded';

// Card elevation variants
export type CardElevation = 'S' | 'L';

// Card overflow variants
export type CardOverflow = 'hidden';

// Card props
export interface CardProps {
  /** Corner style of the card */
  corners?: CardCorners;
  /** Shadow elevation of the card */
  elevation?: CardElevation;
  /** Overflow behavior */
  overflow?: CardOverflow;
  /** Whether the card should take full width */
  fullWidth?: boolean;
  /** Custom style for the card */
  style?: StyleProp<ViewStyle>;
  /** Child components */
  children?: ReactNode;
}

// Card Title props
export interface CardTitleProps {
  title: string;
  subtitle?: string;
  left?: ReactNode;
  right?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card Content props
export interface CardContentProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card Cover props
export interface CardCoverProps {
  source: ImageSourcePropType;
  style?: StyleProp<ImageStyle>;
}

// Card Actions props
export interface CardActionsProps {
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
}

// Card component
export const Card: React.FC<CardProps> = ({
  corners = 'rounded',
  elevation = 'S',
  overflow,
  fullWidth = true,
  style,
  children,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  // Get shadow style based on elevation
  const getShadowStyle = (): ViewStyle => {
    return elevation === 'L' ? tokens.shadows.boxShadows.l.boxShadow : tokens.shadows.boxShadows.s.boxShadow;
  };

  // Get corner style
  const getCornerStyle = (): ViewStyle => {
    return corners === 'rounded'
      ? { borderRadius: theme.borders.components.card.borderRadius }
      : { borderRadius: 0 };
  };

  // Get overflow style
  const getOverflowStyle = (): ViewStyle | undefined => {
    return overflow === 'hidden' ? { overflow: 'hidden' } : undefined;
  };

  // Dynamic styles that depend on theme
  const cardStyles = {
    card: {
      backgroundColor: theme.colors.background,
      width: '100%' as const,
      display: 'flex' as const,
      flexDirection: 'column' as const,
    },
    fullWidth: {
      width: '100%' as const,
    },
  };

  return (
    <View
      style={[
        cardStyles.card,
        getCornerStyle(),
        getShadowStyle(),
        getOverflowStyle(),
        fullWidth && cardStyles.fullWidth,
        style,
      ]}
    >
      {children}
    </View>
  );
};

// Card.Title component
export const CardTitle: React.FC<CardTitleProps> = ({
  title,
  subtitle,
  left,
  right,
  style,
}) => {
  const { theme } = useAppTheme();

  const titleStyles = {
    title: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      paddingHorizontal: theme.spacing[4],
      paddingVertical: theme.spacing[3],
    },
    titleContent: {
      flex: 1,
    },
    titleLeft: {
      marginRight: theme.spacing[3],
    },
    titleRight: {
      marginLeft: theme.spacing[3],
    },
  };

  return (
    <View style={[titleStyles.title, style]}>
      {left && <View style={titleStyles.titleLeft}>{left}</View>}
      <View style={titleStyles.titleContent}>
        <Heading level={6}>{title}</Heading>
        {subtitle && <Text variant="bodySmall">{subtitle}</Text>}
      </View>
      {right && <View style={titleStyles.titleRight}>{right}</View>}
    </View>
  );
};

// Card.Content component
export const CardContent: React.FC<CardContentProps> = ({
  children,
  style,
}) => {
  return <Box padding="8" style={style}>{children}</Box>;
};

// Card.Cover component
export const CardCover: React.FC<CardCoverProps> = ({
  source,
  style,
}) => {
  // Dynamic styles that depend on theme
  const coverStyles = {
    cover: {
      width: '100%' as const,
      height: 200,
    },
  };

  return (
    <Image
      source={source}
      style={[coverStyles.cover, style]}
      resizeMode="cover"
    />
  );
};

// Card.Actions component
export const CardActions: React.FC<CardActionsProps> = ({
  children,
  style,
}) => {
  // Use theme instead of direct token references
  const { theme } = useAppTheme();

  // Dynamic styles that depend on theme
  const actionStyles = {
    actions: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'flex-end' as const,
      paddingHorizontal: theme.spacing[2],
      paddingVertical: theme.spacing[1],
    },
  };

  return <View style={[actionStyles.actions, style]}>{children}</View>;
};

// Add subcomponents to Card
const CardWithSubcomponents = Card as typeof Card & {
  Title: typeof CardTitle;
  Content: typeof CardContent;
  Cover: typeof CardCover;
  Actions: typeof CardActions;
};

CardWithSubcomponents.Title = CardTitle;
CardWithSubcomponents.Content = CardContent;
CardWithSubcomponents.Cover = CardCover;
CardWithSubcomponents.Actions = CardActions;

export default CardWithSubcomponents;
