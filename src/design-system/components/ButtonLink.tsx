/**
 * ButtonLink component for the design system
 */

import React from 'react';
import {
  GestureResponderEvent,
  Linking,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';
import { Text } from './Text';
import VisuallyHidden from './VisuallyHidden';

// ButtonLink action variants
export type ButtonLinkAction = 'primary' | 'secondary' | 'primaryRebrand';

// ButtonLink sizes
export type ButtonLinkSize = 'compact' | 'regular';

// ButtonLink tone
export type ButtonLinkTone = 'onLight' | 'onColor';

// ButtonLink props
export interface ButtonLinkProps extends Omit<TouchableOpacityProps, 'children'> {
  /** Emphasises the importance of your action with colors */
  action?: ButtonLinkAction;
  /** The size of the ButtonLink */
  size?: ButtonLinkSize;
  /** Changes the tone based on the background color. Note: This **only** has an effect on **primary** action buttons. */
  tone?: ButtonLinkTone;
  /** URL to navigate to when pressed */
  href: string;
  /** Target for the link (e.g., '_blank') */
  target?: string;
  /** Accessible label for the button */
  label?: string;
  /** Button content */
  children: React.ReactNode;
  /** Optional style for the button content */
  contentStyle?: StyleProp<ViewStyle>;
  /** Optional style for the button text */
  labelStyle?: StyleProp<TextStyle>;
  /** Optional button color override */
  buttonColor?: string;
  /** Optional text color override */
  textColor?: string;
  /** Optional onPress handler */
  onPress?: (e: GestureResponderEvent) => void;
}

// ButtonLink styles
const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: tokens.spacing.sizes.buttonMinWidth,
    position: 'relative',
    overflow: 'hidden',
  },
  buttonInner: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: tokens.spacing.space[3],
  },
  buttonText: {
    fontFamily: tokens.typography.fontFamilies.base,
    fontWeight: '700',
  },
  compactButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  regularButton: {
    minWidth: tokens.spacing.sizes.buttonMinWidth,
  },
  compactContent: {
    paddingVertical: tokens.spacing.space[3],
    paddingHorizontal: tokens.spacing.space[4],
  },
  regularContent: {
    paddingVertical: tokens.spacing.space[4],
    paddingHorizontal: tokens.spacing.space[4],
  },
  chevron: {
    fontSize: 20,
    fontWeight: '700',
    marginLeft: tokens.spacing.space[2],
  },
});

// ButtonLink component
export const ButtonLink: React.FC<ButtonLinkProps> = ({
  action = 'primary',
  size = 'regular',
  tone = 'onLight',
  href,
  target,
  label,
  style,
  contentStyle,
  labelStyle,
  buttonColor,
  textColor,
  children,
  onPress,
  ...rest
}) => {
  // Handle press event
  const handlePress = (event: GestureResponderEvent) => {
    if (onPress) {
      onPress(event);
    }
    
    if (href) {
      Linking.openURL(href);
    }
  };

  // Get border radius based on action
  const getBorderRadius = () => {
    if (action === 'primaryRebrand') {
      return tokens.borders.componentBorders.button.borderRadiusRebrand;
    }
    return tokens.borders.componentBorders.button.borderRadiusDefault;
  };

  // Get button background color based on action
  const getButtonBackgroundColor = () => {
    if (action === 'primary' && tone === 'onColor') {
      return tokens.colors.backgroundColors.backgroundPrimary;
    }
    if (action === 'primary' || action === 'primaryRebrand') {
      return tokens.colors.secondaryColors.accentGreen700;
    }
    return 'transparent'; // For secondary buttons
  };

  // Get button border color and width
  const getButtonBorder = () => {
    if (action === 'secondary') {
      return {
        borderWidth: tokens.borders.borderWidths.m,
        borderColor: tokens.colors.secondaryColors.accentGreen700,
      };
    }
    return {};
  };

  // Get text color based on action
  const getTextColor = () => {
    if (action === 'primary' && tone === 'onColor') {
      return tokens.colors.textColors.textBrand;
    }
    if (action === 'secondary') {
      return tokens.colors.textColors.textBrand;
    }
    if (action === 'primary' || action === 'primaryRebrand') {
      return tokens.colors.neutralColors.neutralWhite;
    }
    return tokens.colors.textColors.textPrimary;
  };

  // Get size styles
  const getSizeStyles = () => {
    return size === 'compact' ? styles.compactContent : styles.regularContent;
  };

  // Determine if we should show a chevron
  const shouldShowChevron = () => {
    // In the original design, chevrons were shown for primary buttons
    // but not for secondary buttons or compact size
    return action === 'primary' && size === 'regular';
  };

  // Render the button content
  const renderContent = () => {
    // Check if children is a string or contains an icon
    let icon = null;
    let text = children;

    // If children is an array, check for icon
    if (React.Children.count(children) > 1) {
      const childrenArray = React.Children.toArray(children);
      // Assume first child is an icon if it's not a string
      if (typeof childrenArray[0] !== 'string') {
        icon = childrenArray[0];
        text = childrenArray.slice(1);
      }
    }

    return (
      <View style={[styles.buttonInner, getSizeStyles(), contentStyle]}>
        {icon && <View>{icon}</View>}
        {typeof text === 'string' ? (
          <Text
            style={[
              styles.buttonText,
              { color: textColor || getTextColor() },
              labelStyle,
            ]}
          >
            {text}
          </Text>
        ) : (
          text
        )}
        {shouldShowChevron() && (
          <Text style={[styles.chevron, { color: getTextColor() }]}>›</Text>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={handlePress}
      style={[
        styles.button,
        {
          backgroundColor: buttonColor || getButtonBackgroundColor(),
          borderRadius: getBorderRadius(),
          ...getButtonBorder(),
        },
        style,
      ]}
      accessibilityRole="link"
      accessibilityLabel={label || (typeof children === 'string' ? children : undefined)}
      accessibilityHint={target === '_blank' ? 'Opens in a new window' : undefined}
      {...rest}
    >
      {label && <VisuallyHidden>{label}</VisuallyHidden>}
      {renderContent()}
    </TouchableOpacity>
  );
};

export default ButtonLink;
