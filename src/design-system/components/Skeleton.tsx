/**
 * Skeleton component for the design system
 */

import React from 'react';
import { Animated, StyleSheet, View, ViewProps, ViewStyle } from 'react-native';
import tokens from '../tokens';

// Skeleton variant types
export type SkeletonVariant = 'text' | 'circle' | 'rectangular';

// Skeleton props
export interface SkeletonProps extends ViewProps {
  /** The type of skeleton to display */
  variant?: SkeletonVariant;
  /** Width of the skeleton */
  width?: number | string;
  /** Height of the skeleton */
  height?: number | string;
  /** Child components (will be hidden) */
  children?: React.ReactNode;
}

// Skeleton styles
const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: tokens.colors.backgroundColors.backgroundTertiary,
    borderRadius: tokens.borders.radii.m,
    overflow: 'hidden',
  },
  circle: {
    borderRadius: tokens.borders.radii.round,
  },
  rectangular: {
    borderRadius: 0,
  },
  text: {
    height: 'auto',
    transform: [{ scaleY: 0.8 }],
  },
  childContainer: {
    opacity: 0,
  },
});

// Skeleton component
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  style,
  children,
  ...rest
}) => {
  // Create animation value
  const opacity = React.useRef(new Animated.Value(1)).current;

  // Start animation on mount
  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.5,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [opacity]);

  // Get variant style
  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case 'circle':
        return styles.circle;
      case 'rectangular':
        return styles.rectangular;
      case 'text':
      default:
        return styles.text;
    }
  };

  // Get dimensions style
  const getDimensionsStyle = (): ViewStyle => {
    const dimensionsStyle: ViewStyle = {};

    if (width !== undefined) {
      dimensionsStyle.width = typeof width === 'string' ? parseInt(width, 10) : width;
    }
    if (height !== undefined) {
      dimensionsStyle.height = typeof height === 'string' ? parseInt(height, 10) : height;
    }

    return dimensionsStyle;
  };

  // If children are provided, measure them to get dimensions
  const childrenRef = React.useRef<View>(null);
  const [childDimensions, setChildDimensions] = React.useState<{ width?: number; height?: number }>({});

  const onChildLayout = React.useCallback((event: { nativeEvent: { layout: { width: number, height: number } } }) => {
    const { width, height } = event.nativeEvent.layout;
    setChildDimensions({ width, height });
  }, []);

  // Create combined style
  const combinedStyle = [
    styles.skeleton,
    getVariantStyle(),
    getDimensionsStyle(),
    children ? childDimensions : {},
    style,
  ];

  return (
    <Animated.View
      style={[combinedStyle, { opacity }]}
      {...rest}
    >
      {children && (
        <View
          ref={childrenRef}
          style={styles.childContainer}
          onLayout={onChildLayout}
          accessibilityElementsHidden={true}
          importantForAccessibility="no"
        >
          {children}
        </View>
      )}
    </Animated.View>
  );
};

export default Skeleton;
