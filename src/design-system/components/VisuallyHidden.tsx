/**
 * VisuallyHidden component for the design system
 */

import React from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { Text } from './Text';

// VisuallyHidden props
export interface VisuallyHiddenProps extends ViewProps {
  /** Content to be visually hidden but accessible to screen readers */
  children: React.ReactNode;
  /** Optional ID for the element */
  id?: string;
  /** Optional ARIA live region setting */
  ariaLive?: 'polite' | 'assertive' | 'none';
}

// VisuallyHidden styles
const styles = StyleSheet.create({
  visuallyHidden: {
    position: 'absolute',
    width: 1,
    height: 1,
    padding: 0,
    margin: -1,
    overflow: 'hidden',
    // In React Native, we can't use clip, so we use opacity
    opacity: 0,
    // Position off-screen
    left: -10000,
    top: -10000,
  },
});

// VisuallyHidden component
export const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({
  children,
  id,
  ariaLive,
  style,
  ...rest
}) => {
  return (
    <View
      style={[styles.visuallyHidden, style]}
      accessibilityElementsHidden={false}
      importantForAccessibility="yes"
      accessibilityLiveRegion={ariaLive}
      {...rest}
    >
      {typeof children === 'string' ? (
        <Text id={id}>{children}</Text>
      ) : (
        children
      )}
    </View>
  );
};

export default VisuallyHidden;
