/**
 * Grid component for the design system
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native-unistyles';
import tokens from '../tokens';

// Grid alignment values
type AlignValue = 'start' | 'center' | 'end' | 'stretch' | 'baseline';

// FlexAlign type for React Native
type FlexAlignType = 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';

// JustifyContent type for React Native
type JustifyContentType = 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';

// Grid flow values
type FlowValue = 'row' | 'column' | 'dense' | 'rowDense' | 'columnDense';

// Grid columns values
type ColumnsValue = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | '12';

// Grid gap values
type GapValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '8' | '10' | '12' | '16' | '24' | '32';

// Grid props
export interface GridProps extends ViewProps {
  /** Alignment of the grid items on the vertical axis */
  alignY?: AlignValue;
  /** Alignment of the grid items on the horizontal axis */
  alignX?: AlignValue;
  /** Justification of the grid content */
  justifyContent?: AlignValue;
  /** Flow direction of the grid items */
  flow?: FlowValue;
  /** Number of columns in the grid */
  columns?: ColumnsValue;
  /** Gap between grid items */
  gap?: GapValue;
  /** Gap between rows */
  rowGap?: GapValue;
  /** Gap between columns */
  columnGap?: GapValue;
  /** Whether the grid should be displayed inline */
  inline?: boolean;
  /** Grid template columns */
  gridTemplateColumns?: string;
  /** Grid template rows */
  gridTemplateRows?: string;
  /** Grid template areas */
  gridTemplateAreas?: string;
  /** Child components */
  children: React.ReactNode;
}

// Grid item props
export interface GridItemProps extends ViewProps {
  /** Grid area name */
  gridArea?: string;
  /** Grid column start */
  gridColumnStart?: string | number;
  /** Grid column end */
  gridColumnEnd?: string | number;
  /** Grid column span */
  gridColumn?: string;
  /** Grid row start */
  gridRowStart?: string | number;
  /** Grid row end */
  gridRowEnd?: string | number;
  /** Grid row span */
  gridRow?: string;
  /** Order of the grid item */
  order?: number;
  /** Alignment of the grid item */
  alignSelf?: AlignValue;
  /** Justification of the grid item */
  justifySelf?: AlignValue;
  /** Child components */
  children: React.ReactNode;
}

// Grid styles
const styles = StyleSheet.create({
  grid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  gridItem: {
    flexGrow: 0,
    flexShrink: 0,
  },
});

// GridItem component
const GridItem: React.FC<GridItemProps> = ({
  gridArea,
  gridColumnStart,
  gridColumnEnd,
  gridColumn,
  gridRowStart,
  gridRowEnd,
  gridRow,
  order,
  alignSelf,
  justifySelf,
  style,
  children,
  ...rest
}) => {
  // Parse grid column value
  const parseGridColumn = (): ViewStyle => {
    if (!gridColumn) return {};

    // Simple implementation for common patterns
    if (gridColumn === '1/-1' || gridColumn === '1 / -1') {
      return { width: '100%' };
    }

    // For more complex patterns, we'd need a more sophisticated parser
    return {};
  };

  // Get flex basis based on grid column span
  const getFlexBasis = (): number | undefined => {
    if (gridColumn) {
      const parts = gridColumn.split('/').map(p => p.trim());
      if (parts.length === 2) {
        const start = parseInt(parts[0], 10);
        const end = parseInt(parts[1], 10);
        if (!isNaN(start) && !isNaN(end)) {
          const span = end - start;
          // Return as a percentage (0-1) for React Native
          return (span / 12) * 100;
        }
      }
    }
    return undefined;
  };

  // Convert alignSelf to React Native compatible value
  const getAlignSelf = (): FlexAlignType | 'auto' | undefined => {
    if (!alignSelf) return undefined;

    switch (alignSelf) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      case 'stretch': return 'stretch';
      case 'baseline': return 'baseline';
      default: return undefined;
    }
  };

  // Create style object
  const itemStyle: ViewStyle = {
    ...(order !== undefined && { order }),
    ...(alignSelf && { alignSelf: getAlignSelf() }),
    ...parseGridColumn(),
    ...(getFlexBasis() && { flexBasis: getFlexBasis() }),
  };

  return (
    <View style={[styles.gridItem, itemStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Grid component
const GridComponent: React.FC<GridProps> = ({
  alignY,
  alignX,
  justifyContent,
  flow,
  columns,
  gap,
  rowGap,
  columnGap,
  inline,
  gridTemplateColumns,
  gridTemplateRows,
  gridTemplateAreas,
  style,
  children,
  ...rest
}) => {
  // Get alignment style
  const getAlignItems = (): FlexAlignType | undefined => {
    if (!alignY) return undefined;

    switch (alignY) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      case 'stretch': return 'stretch';
      case 'baseline': return 'baseline';
      default: return undefined;
    }
  };

  // Get justify items style
  const getJustifyItems = (): JustifyContentType | undefined => {
    if (!alignX) return undefined;

    switch (alignX) {
      case 'start': return 'flex-start';
      case 'center': return 'center';
      case 'end': return 'flex-end';
      // 'stretch' is not valid for justifyContent in React Native
      case 'stretch': return 'center';
      default: return undefined;
    }
  };

  // Get gap value
  const getGapValue = (value?: GapValue): number | undefined => {
    if (!value) return undefined;
    return tokens.spacing.space[value];
  };

  // Create style object
  const gridStyle: ViewStyle = {
    ...(alignY && { alignItems: getAlignItems() }),
    ...(alignX && { justifyContent: getJustifyItems() }),
    ...(gap && { gap: getGapValue(gap) }),
    ...(rowGap && { rowGap: getGapValue(rowGap) }),
    ...(columnGap && { columnGap: getGapValue(columnGap) }),
  };

  return (
    <View style={[styles.grid, gridStyle, style]} {...rest}>
      {children}
    </View>
  );
};

// Add GridItem to Grid component
type GridComponentType = typeof GridComponent & {
  Item: typeof GridItem;
};

// Create Grid with GridItem
const Grid = GridComponent as GridComponentType;
Grid.Item = GridItem;

// Display names
GridComponent.displayName = 'Grid';
GridItem.displayName = 'Grid.Item';

export default Grid;
