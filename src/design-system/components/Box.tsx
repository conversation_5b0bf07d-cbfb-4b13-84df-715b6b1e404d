/**
 * Box component for the design system
 * Theme-aware implementation
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { useAppTheme } from '../provider';

// Spacing values
type SpacingValue = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '10' | '12' | '16' | '24' | '32';

// Border radius values
type BorderRadiusValue = 's' | 'm' | 'l' | 'none' | 'round';

// Background color values
type BackgroundColorValue =
  | 'backgroundPrimary'
  | 'backgroundSecondary'
  | 'backgroundTertiary'
  | 'backgroundScrim'
  | 'backgroundDark'
  | 'backgroundPressed'
  | 'feedbackBackgroundError'
  | 'feedbackBackgroundSuccess'
  | 'feedbackBackgroundWarning'
  | 'feedbackBackgroundInfo';

// Overflow values
type OverflowValue = 'hidden' | 'visible' | 'scroll';

// Box props
export interface BoxProps extends ViewProps {
  /** Background color of the box */
  backgroundColor?: BackgroundColorValue;
  /** Border radius of all corners */
  borderRadius?: BorderRadiusValue;
  /** Border radius of top left corner */
  borderTopLeftRadius?: BorderRadiusValue;
  /** Border radius of top right corner */
  borderTopRightRadius?: BorderRadiusValue;
  /** Border radius of bottom left corner */
  borderBottomLeftRadius?: BorderRadiusValue;
  /** Border radius of bottom right corner */
  borderBottomRightRadius?: BorderRadiusValue;
  /** Overflow behavior */
  overflow?: OverflowValue;
  /** Padding on all sides */
  padding?: SpacingValue;
  /** Padding on top and bottom */
  paddingY?: SpacingValue;
  /** Padding on left and right */
  paddingX?: SpacingValue;
  /** Padding on top */
  paddingTop?: SpacingValue;
  /** Padding on bottom */
  paddingBottom?: SpacingValue;
  /** Padding on left */
  paddingLeft?: SpacingValue;
  /** Padding on right */
  paddingRight?: SpacingValue;
  /** Child components */
  children: React.ReactNode;
}

// Box component
export const Box = React.forwardRef<View, BoxProps>(
  (
    {
      backgroundColor,
      borderRadius,
      borderTopLeftRadius,
      borderTopRightRadius,
      borderBottomLeftRadius,
      borderBottomRightRadius,
      overflow,
      padding,
      paddingY,
      paddingX,
      paddingTop,
      paddingBottom,
      paddingLeft,
      paddingRight,
      style,
      children,
      ...rest
    },
    ref
  ) => {
    // Use theme instead of direct token references
    const { theme } = useAppTheme();

    // Get background color
    const getBackgroundColor = (): string | undefined => {
      if (!backgroundColor) return undefined;

      // Map background color to theme value
      switch (backgroundColor) {
        case 'backgroundPrimary':
          return theme.colors.backgroundPrimary;
        case 'backgroundSecondary':
          return theme.colors.backgroundSecondary;
        case 'backgroundTertiary':
          return theme.colors.backgroundTertiary;
        case 'backgroundScrim':
          return theme.colors.backgroundScrim;
        case 'backgroundDark':
          return theme.colors.backgroundDark;
        case 'backgroundPressed':
          return theme.colors.backgroundPressed;
        case 'feedbackBackgroundError':
          return theme.colors.feedbackBackgroundError;
        case 'feedbackBackgroundSuccess':
          return theme.colors.feedbackBackgroundSuccess;
        case 'feedbackBackgroundWarning':
          return theme.colors.feedbackBackgroundWarning;
        case 'feedbackBackgroundInfo':
          return theme.colors.feedbackBackgroundInfo;
        default:
          return undefined;
      }
    };

    // Get border radius value
    const getBorderRadiusValue = (value?: BorderRadiusValue): number | undefined => {
      if (!value) return undefined;
      if (value === 'none') return 0;

      return theme.borders.radii[value];
    };

    // Get padding value
    const getPaddingValue = (value?: SpacingValue): number | undefined => {
      if (!value) return undefined;
      return theme.spacing[value];
    };

    // Create style object
    const boxStyle: ViewStyle = {
      ...(backgroundColor && { backgroundColor: getBackgroundColor() }),
      ...(borderRadius && { borderRadius: getBorderRadiusValue(borderRadius) }),
      ...(borderTopLeftRadius && { borderTopLeftRadius: getBorderRadiusValue(borderTopLeftRadius) }),
      ...(borderTopRightRadius && { borderTopRightRadius: getBorderRadiusValue(borderTopRightRadius) }),
      ...(borderBottomLeftRadius && { borderBottomLeftRadius: getBorderRadiusValue(borderBottomLeftRadius) }),
      ...(borderBottomRightRadius && { borderBottomRightRadius: getBorderRadiusValue(borderBottomRightRadius) }),
      ...(overflow && { overflow }),
      ...(padding && { padding: getPaddingValue(padding) }),
      ...(paddingY && { paddingVertical: getPaddingValue(paddingY) }),
      ...(paddingX && { paddingHorizontal: getPaddingValue(paddingX) }),
      ...(paddingTop && { paddingTop: getPaddingValue(paddingTop) }),
      ...(paddingBottom && { paddingBottom: getPaddingValue(paddingBottom) }),
      ...(paddingLeft && { paddingLeft: getPaddingValue(paddingLeft) }),
      ...(paddingRight && { paddingRight: getPaddingValue(paddingRight) }),
    };

    return (
      <View ref={ref} style={[boxStyle, style]} {...rest}>
        {children}
      </View>
    );
  }
);

Box.displayName = 'Box';

export default Box;
