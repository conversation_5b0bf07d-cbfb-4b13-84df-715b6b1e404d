/**
 * Theme provider component for the design system
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { UnistylesRuntime } from 'react-native-unistyles';
import { AppTheme, themes } from './theme';

// Create theme context
type ThemeContextType = {
  theme: AppTheme;
  isDarkMode: boolean;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Custom hook to use theme
export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme provider props
type ThemeProviderProps = {
  children: React.ReactNode;
};

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Always use light theme as requested
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Initialize theme on mount for web
  useEffect(() => {
    // Set initial theme for web
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

      // Apply theme colors directly to root element for better web compatibility
      if (isDarkMode) {
        // Dark theme
        document.documentElement.style.setProperty('--text-color', themes.dark.colors.textPrimary);
        document.documentElement.style.setProperty('--bg-color', themes.dark.colors.background);
      } else {
        // Light theme
        document.documentElement.style.setProperty('--text-color', themes.light.colors.textPrimary);
        document.documentElement.style.setProperty('--bg-color', themes.light.colors.background);
      }

      // Add global styles for scrolling
      const style = document.createElement('style');
      style.textContent = `
        html, body, #root {
          height: 100%;
          width: 100%;
          margin: 0;
          padding: 0;
          overflow-y: auto;
          color: var(--text-color);
          background-color: var(--bg-color);
        }
      `;
      document.head.appendChild(style);
    }
  }, [isDarkMode]);

  // Always use light theme regardless of device settings
  useEffect(() => {
    if (UnistylesRuntime.hasAdaptiveThemes) {
      // Force light theme
      setIsDarkMode(false);

      // Update web theme to always use light
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', 'light');
      }
    }
  }, []);

  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkMode((prev) => {
      const newMode = !prev;

      // Update Unistyles theme
      UnistylesRuntime.setTheme(newMode ? 'dark' : 'light');

      // For web platform, also update the HTML element's data-theme attribute
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', newMode ? 'dark' : 'light');

        // Apply theme colors directly to root element for better web compatibility
        if (newMode) {
          // Dark theme
          document.documentElement.style.setProperty('--text-color', themes.dark.colors.textPrimary);
          document.documentElement.style.setProperty('--bg-color', themes.dark.colors.background);
        } else {
          // Light theme
          document.documentElement.style.setProperty('--text-color', themes.light.colors.textPrimary);
          document.documentElement.style.setProperty('--bg-color', themes.light.colors.background);
        }

        // Force a re-render on web by triggering a small resize event
        window.dispatchEvent(new Event('resize'));
      }

      return newMode;
    });
  };

  // Get current theme
  const theme = isDarkMode ? themes.dark : themes.light;

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    isDarkMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
