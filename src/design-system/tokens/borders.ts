/**
 * Border tokens
 */

import { borderColors } from './colors';
import { GRID } from './spacing';

// Border widths
export const borderWidths = {
  none: 0,
  s: 1,
  m: 2,
  l: 3,
};

// Border radii
export const radii = {
  none: 0,
  xs: GRID,
  s: GRID * 2,
  m: GRID * 4,
  l: GRID * 6,
  xl: GRID * 8,
  round: 9999,
};

// Outlines for focus states
export const outlines = {
  outlineFocus: {
    width: borderWidths.m,
    color: borderColors.borderFocus,
  },
  outlineInputFocus: {
    width: borderWidths.s,
    color: borderColors.borderFocus,
  },
};

// Component-specific border tokens
export const componentBorders = {
  button: {
    borderRadiusDefault: radii.s,
    borderRadiusRebrand: radii.m,
    borderWidthSecondary: borderWidths.m,
    focusOutlineWidth: borderWidths.s,
  },
  badge: {
    borderRadius: radii.s,
  },
  card: {
    borderRadius: radii.m,
  },
  input: {
    borderRadius: radii.s,
    borderWidth: borderWidths.s,
  },
};
