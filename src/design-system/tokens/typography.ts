/**
 * Typography tokens
 */

// Base font size
export const BASE_FONT_SIZE = 10;
export const BASE_FONT_RELATIVE = `${(BASE_FONT_SIZE / 16) * 100}%`;

// Font families
export const fontFamilies = {
  base: "Etelka",
  heading: "Etelka",
};

// Font weights
export const fontWeights = {
  light: "300",
  medium: "500",
  bold: "700",
  black: "900",

  // Semantic weights
  bodyRegular: "300",
  bodyBold: "500",
  headingLight: "500",
  headingRegular: "700",
};

// Font sizes
export const fontSizes = {
  // Numeric sizes
  12: 12,
  14: 14,
  16: 16,
  18: 18,
  20: 20,
  24: 24,
  32: 32,
  40: 40,
  48: 48,
  64: 64,
  80: 80,

  // Semantic sizes
  "3XS": 16,
  "2XS": 18,
  XS: 20,
  S: 24,
  M: 32,
  L: 40,
  XL: 48,
  "2XL": 64,
  "3XL": 80,

  // Body sizes
  BodyXS: 12,
  BodyS: 14,
  BodyM: 16,
  BodyL: 18,
  BodyXL: 20,
};

// Letter spacings
export const letterSpacings = {
  narrowM: -2,
  narrowS: -1,
  normal: 0,
  wide: 0.3,
};

// Line heights
export const lineHeights = {
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  9: 36,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
};

// Typography variants for body text
export const bodyTypography = {
  bodyXL: {
    fontSize: fontSizes.BodyXL,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyL: {
    fontSize: fontSizes.BodyL,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyM: {
    fontSize: fontSizes.BodyM,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyS: {
    fontSize: fontSizes.BodyS,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[5],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  bodyXS: {
    fontSize: fontSizes.BodyXS,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[4],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  quoteM: {
    fontSize: fontSizes[32],
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[9],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
  quoteS: {
    fontSize: fontSizes[24],
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
    fontFamily: fontFamilies.base,
    fontWeight: fontWeights.bodyRegular,
  },
};

// Typography variants for headings
export const headingTypography = {
  heading3xl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["3XL"],
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[20],
  },
  heading2xl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["2XL"],
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[16],
  },
  headingXl: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.XL,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowM,
    lineHeight: lineHeights[12],
  },
  headingL: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.L,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.narrowS,
    lineHeight: lineHeights[10],
  },
  headingM: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.M,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[9],
  },
  headingS: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.S,
    fontWeight: fontWeights.headingRegular,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[7],
  },
  headingXs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes.XS,
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
  },
  heading2xs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["2XS"],
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.normal,
    lineHeight: lineHeights[6],
  },
  heading3xs: {
    fontFamily: fontFamilies.base,
    fontSize: fontSizes["3XS"],
    fontWeight: fontWeights.headingLight,
    letterSpacing: letterSpacings.wide,
    lineHeight: lineHeights[5],
  },
};

// Map to React Native Paper typography variants
export const paperTypographyMapping = {
  displayLarge: headingTypography.heading3xl,
  displayMedium: headingTypography.heading2xl,
  displaySmall: headingTypography.headingXl,

  headlineLarge: headingTypography.headingL,
  headlineMedium: headingTypography.headingM,
  headlineSmall: headingTypography.headingS,

  titleLarge: headingTypography.headingXs,
  titleMedium: headingTypography.heading2xs,
  titleSmall: headingTypography.heading3xs,

  bodyLarge: bodyTypography.bodyXL,
  bodyMedium: bodyTypography.bodyM,
  bodySmall: bodyTypography.bodyS,

  labelLarge: bodyTypography.bodyL,
  labelMedium: bodyTypography.bodyM,
  labelSmall: bodyTypography.bodyXS,
};
