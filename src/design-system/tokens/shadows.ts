/**
 * Shadow tokens
 */

import { borderWidths } from "./borders";
import { borderColors, formColors } from "./colors";

// Border shadows (outlines)
export const borderShadows = {
  shadowHover: {
    boxShadow: {
      color: borderColors.outlineHover,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.l,
    },
    elevation: 0,
  },
  shadowError: {
    boxShadow: {
      color: formColors.formOutlineError,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.l,
    },
    elevation: 0,
  },
  shadowSelected: {
    boxShadow: {
      color: borderColors.borderSelected,
      offset: { width: 0, height: 0 },
      opacity: 1,
      radius: borderWidths.m,
    },
    elevation: 0,
  },
};

// Box shadows
export const boxShadows = {
  xs: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.2)",
      offset: { width: 0, height: 3 },
      opacity: 1,
      radius: 5,
    },
    elevation: 3,
  },
  s: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.1)",
      offset: { width: 0, height: 5 },
      opacity: 1,
      radius: 10,
    },
    elevation: 6,
  },
  m: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.05)",
      offset: { width: 0, height: 10 },
      opacity: 1,
      radius: 15,
    },
    elevation: 10,
  },
  l: {
    boxShadow: {
      color: "rgba(26, 23, 27, 0.2)",
      offset: { width: 0, height: 10 },
      opacity: 1,
      radius: 30,
    },
    elevation: 15,
  },
};

// Component-specific shadows
export const componentShadows = {
  card: boxShadows.s,
  modal: boxShadows.l,
  dropdown: boxShadows.m,
  button: boxShadows.xs,
};
