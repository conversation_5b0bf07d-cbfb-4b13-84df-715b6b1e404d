/**
 * Color tokens
 */

// Base color palette
export const brandColors = {
  brandRed: "#E5384C",
  brandOrange: "#EA714F",
  brandDarkRed: "#D21242",
  brandLightRed: "#F9C7CC",
};

export const brandGradients = {
  brandGradientStart: brandColors.brandRed,
  brandGradientEnd: brandColors.brandOrange,
  brandGradientDark: "linear-gradient(90deg, #B4334E 0%, #B65A4E 100%)",
};

export const brandSupport = {
  darkerRed: "#A63B3C",
  darkPurple: "#300C38",
};

export const neutralColors = {
  neutralWhite: "#FFF",
  neutral50: "#FCFAFA",
  neutral100: "#F8F6F6",
  neutral300: "#F3F0F0",
  neutral400: "#DFDCDC",
  neutral800: "#716A6A",
  neutral900: "#2F2D2D",
  neutralBlack: "#000",
};

// Opacity colors
export const opacityColors = {
  whiteOpacity15: `${neutralColors.neutralWhite}15`,
  whiteOpacity30: `${neutralColors.neutralWhite}30`,
  blackOpacity40: "#00000040",
  blackOpacity70: `${neutralColors.neutral900}70`,
};

// Secondary colors
export const secondaryColors = {
  // Purple
  purple50: "#F4F3FA",
  purple100: "#E8E6F4",
  purple300: "#C3C6E5",
  purple500: "#8D8CC6",
  purple700: "#8586CF",
  purple800: "#655790",
  purple900: "#3E235B",

  // Pink
  pink50: "#FDEDF1",
  pink100: "#FBDBE3",
  pink300: "#F6B1C6",
  pink500: "#EC6C87",
  pink700: "#C44B6B",
  pink800: "#A04967",
  pink900: "#552748",

  // Blue
  blue50: "#F0FAF8",
  blue100: "#E1F4F1",
  blue300: "#B1DDDF",
  blue500: "#72BDCE",
  blue700: "#3E798D",
  blue900: "#09354B",

  // Bluegray
  blueGray50: "#EBF6F0",
  blueGray100: "#D7EDE1",
  blueGray300: "#BDE0D7",
  blueGray500: "#85BAB0",
  blueGray700: "#557C77",
  blueGray900: "#243D3D",

  // Green
  green50: "#F2F7EC",
  green100: "#E4EFD8",
  green300: "#CDE3BB",
  green500: "#7EC389",
  green700: "#009b65",
  green800: "#2C6F49",
  green900: "#0A4033",

  // Accentgreen
  accentGreen100: "#e3faea",
  accentGreen200: "#c0eaca",
  accentGreen300: "#84dc99",
  accentGreen600: "#009b65",
  accentGreen700: "#007250",
  accentGreen800: "#00593f",

  // Yellow
  yellow50: "#FFFAF0",
  yellow100: "#FEF4E0",
  yellow300: "#FDE8B6",
  yellow500: "#FCCA6D",
  yellow700: "#CE7731",
  yellow900: "#501318",

  // Eneco red
  enecoRed600: "#e5384c",
  enecoRed700: "#d21242",
  enecoRed800: "#bf0639",
  enecoRed900: "#821034",

  // Orange
  orange100: "#ffe7dc",
  orange300: "#ffba8f",
  orange400: "#ff9363",
  orange500: "#ea714f",
};

// Semantic color tokens
export const backgroundColors = {
  backgroundPrimary: neutralColors.neutralWhite,
  backgroundSecondary: neutralColors.neutral100,
  backgroundTertiary: neutralColors.neutral300,
  backgroundScrim: opacityColors.blackOpacity70,
  backgroundDark: neutralColors.neutral900,
  backgroundPressed: neutralColors.neutral100,
};

export const backgroundColoredColors = {
  backgroundVarOne: secondaryColors.purple100,
  backgroundVarTwo: secondaryColors.pink100,
  backgroundVarThree: secondaryColors.blue100,
  backgroundVarFour: secondaryColors.blueGray100,
  backgroundVarFive: secondaryColors.green100,
  backgroundVarSix: secondaryColors.yellow100,
};

export const textColors = {
  textPrimary: neutralColors.neutral900,
  textInverted: neutralColors.neutralWhite,
  textBrand: secondaryColors.accentGreen700,
  textOnBackgroundVarOne: secondaryColors.purple900,
  textOnBackgroundVarTwo: secondaryColors.pink900,
  textOnBackgroundVarThree: secondaryColors.blue900,
  textOnBackgroundVarFour: secondaryColors.blueGray900,
  textOnBackgroundVarFive: secondaryColors.green900,
  textOnBackgroundVarSix: secondaryColors.yellow900,
  textLowEmphasis: neutralColors.neutral800,
};

export const iconColors = {
  iconPrimary: neutralColors.neutral900,
  iconSecondary: neutralColors.neutral800,
  iconTertiary: neutralColors.neutral900,
  iconInverted: neutralColors.neutralWhite,
  iconBrand: brandColors.brandDarkRed,
  iconCooling: secondaryColors.blue700,
  iconElectricity: secondaryColors.green700,
  iconGas: secondaryColors.purple700,
  iconHeat: secondaryColors.pink700,
  iconReview: secondaryColors.accentGreen600,
  iconSolar: secondaryColors.yellow700,
  iconTotal: secondaryColors.blueGray700,
  iconWater: secondaryColors.blue700,
};

export const borderColors = {
  borderDividerLowEmphasis: neutralColors.neutral300,
  borderDividerMediumEmphasis: neutralColors.neutral400,
  borderDividerHighEmphasis: neutralColors.neutral900,
  borderFocus: neutralColors.neutral900,
  borderSelected: secondaryColors.green500,
  outlineHover: neutralColors.neutral300,
};

export const linkColors = {
  linkBrand: textColors.textBrand,
  linkPrimary: textColors.textPrimary,
  linkSecondary: textColors.textLowEmphasis,
  linkDisabled: neutralColors.neutral300,
  linkInverted: textColors.textInverted,
};

export const feedbackColors = {
  feedbackError: brandColors.brandDarkRed,
  feedbackSuccess: secondaryColors.green700,
  feedbackWarning: secondaryColors.yellow700,
  feedbackInfo: secondaryColors.blue700,
  feedbackBackgroundError: neutralColors.neutral100,
  feedbackBackgroundSuccess: secondaryColors.green100,
  feedbackBackgroundWarning: secondaryColors.yellow100,
  feedbackBackgroundInfo: secondaryColors.blue100,
};

export const formColors = {
  formBorderDefault: neutralColors.neutral800,
  formBorderError: brandColors.brandDarkRed,
  formBorderHover: neutralColors.neutral900,
  formErrorMessageBackground: brandSupport.darkerRed,
  formOutlineError: brandColors.brandDarkRed,
};

// Combined color palette for easy access
export const palette = {
  ...brandColors,
  ...brandGradients,
  ...brandSupport,
  ...neutralColors,
  ...opacityColors,
  ...secondaryColors,
  ...backgroundColors,
  ...backgroundColoredColors,
  ...textColors,
  ...iconColors,
  ...borderColors,
  ...linkColors,
  ...feedbackColors,
  ...formColors,
};
