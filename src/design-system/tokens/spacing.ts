/**
 * Spacing tokens
 */

// Base grid unit
export const GRID = 4;

// Spacing scale
export const space = {
  0: 0,
  1: GRID * 1,
  2: GRID * 2,
  3: GRID * 3,
  4: GRID * 4,
  5: GRID * 5,
  6: G<PERSON><PERSON> * 6,
  7: <PERSON><PERSON><PERSON> * 7,
  8: G<PERSON><PERSON> * 8,
  10: G<PERSON><PERSON> * 10,
  12: GRID * 12,
  16: GRID * 16,
  24: GRID * 24,
  32: GRID * 32,
};

// Icon sizes
export const iconSizes = {
  small: 16,
  medium: 24,
  large: 32,
  extraLarge: 64,
  illustrationSmall: 40,
  illustrationMedium: 60,
  illustrationLarge: 80,
};

// Sizes for components and layouts
export const sizes = {
  1: 768,
  2: 1024,
  3: 1220,
  4: 1432,
  5: 1680,
  buttonMinWidth: 80,
  targetMinWidth: 44,
  targetMinHeight: 44,
  inputMinHeight: 48,
  ...iconSizes,
};

// Helper function to get spacing value
export const getSpacing = (multiplier: number): number => {
  return GRID * multiplier;
};

// Helper function to get responsive spacing
export const getResponsiveSpacing = (
  base: number,
  compact: number = base / 2
): { compact: number; base: number } => {
  return {
    compact: getSpacing(compact),
    base: getSpacing(base),
  };
};
