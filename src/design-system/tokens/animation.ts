/**
 * Animation tokens
 */

// Transition durations
export const durations = {
  quick: 200,
  medium: 300,
  slow: 500,
};

// Easing functions
export const easings = {
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  linear: 'linear',
};

// Transitions
export const transitions = {
  easeQuick: {
    duration: durations.quick,
    easing: easings.easeInOut,
  },
  easeMedium: {
    duration: durations.medium,
    easing: easings.easeInOut,
  },
  easeSlow: {
    duration: durations.slow,
    easing: easings.easeInOut,
  },
};

// Animation presets
export const animations = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 },
  },
  slideInUp: {
    from: { translateY: 100 },
    to: { translateY: 0 },
  },
  slideOutDown: {
    from: { translateY: 0 },
    to: { translateY: 100 },
  },
};
