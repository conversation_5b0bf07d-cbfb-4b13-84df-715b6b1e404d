import Constants from 'expo-constants';

// Define the shape of our environment variables
interface EnvConfig {
  oktaDomain: string;
  oktaClientId: string;
  oktaRedirectUri: string;
  oktaLogoutRedirectUri: string;
  environment: 'development' | 'test' | 'acceptance' | 'production';

  // Digital Core API configuration
  dcApiBaseUrl: string;
  dcApiKey: string;
}

// Get environment variables from .env file
const getEnvVars = (): EnvConfig => {
  // Determine the current environment from .env or default to 'development'
  const currentEnv = (process.env.APP_ENV || Constants.expoConfig?.extra?.appEnv || 'development') as 'development' | 'test' | 'acceptance' | 'production';
  console.log(`Current environment: ${currentEnv}`);

  // Default values for environment-specific variables
  const defaultValues = {
    development: {
      oktaDomain: 'inloggen.test.eneco.nl',
      oktaRedirectUri: 'http://localhost:8888/api/auth/callback',
      oktaLogoutRedirectUri: 'http://localhost:8888',
      dcApiBaseUrl: 'https://test.api-digital.enecogroup.com/dxpweb',
    },
    test: {
      oktaDomain: 'inloggen.test.eneco.nl',
      oktaRedirectUri: 'https://www.test.eneco.nl/api/auth/callback/okta',
      oktaLogoutRedirectUri: 'eneco-acc.okta-emea.com:/logout',
      dcApiBaseUrl: 'https://test.api-digital.enecogroup.com/dxpweb',
    },
    acceptance: {
      oktaDomain: 'inloggen.acc.eneco.nl',
      oktaRedirectUri: 'https://www.acc.eneco.nl/api/auth/callback/okta',
      oktaLogoutRedirectUri: 'eneco-acc.okta-emea.com:/logout',
      dcApiBaseUrl: 'https://acc.api-digital.enecogroup.com/dxpweb',
    },
    production: {
      oktaDomain: 'inloggen.eneco.nl',
      oktaRedirectUri: 'https://www.eneco.nl/api/auth/callback/okta',
      oktaLogoutRedirectUri: 'eneco-acc.okta-emea.com:/logout',
      dcApiBaseUrl: 'https://api-digital.enecogroup.com/dxpweb',
    },
  };

  // Get the default values for the current environment
  const defaultEnvValues = defaultValues[currentEnv];

  // Debug environment variables in development
  if (__DEV__) {
    console.log('Current environment:', currentEnv);
    console.log('Okta Client ID loaded:', process.env.EXPO_PUBLIC_OKTA_CLIENT_ID ? 'Yes' : 'No');
    console.log('Okta Domain loaded:', process.env.EXPO_PUBLIC_OKTA_DOMAIN ? 'Yes' : 'No');
  }

  if (__DEV__) {
    // For development, read from EXPO_PUBLIC_ prefixed environment variables
    // These are available in the client-side code in Expo
    return {
      // Okta configuration
      oktaDomain: process.env.EXPO_PUBLIC_OKTA_DOMAIN || defaultEnvValues.oktaDomain,
      oktaClientId: process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || '',
      oktaRedirectUri: process.env.EXPO_PUBLIC_OKTA_REDIRECT_URI || defaultEnvValues.oktaRedirectUri,
      oktaLogoutRedirectUri: process.env.EXPO_PUBLIC_OKTA_LOGOUT_REDIRECT_URI || defaultEnvValues.oktaLogoutRedirectUri,

      // Environment
      environment: currentEnv,

      // Digital Core API configuration
      dcApiBaseUrl: process.env.EXPO_PUBLIC_DC_API_BASE_URL || defaultEnvValues.dcApiBaseUrl,
      dcApiKey: process.env.EXPO_PUBLIC_DC_API_KEY || '',
    };
  }

  // For production builds, use the values from app.config.js/app.json
  // These values should be set during the build process
  return {
    oktaDomain: Constants.expoConfig?.extra?.oktaDomain || defaultEnvValues.oktaDomain,
    oktaClientId: Constants.expoConfig?.extra?.oktaClientId || '',
    oktaRedirectUri: Constants.expoConfig?.extra?.oktaRedirectUri || defaultEnvValues.oktaRedirectUri,
    oktaLogoutRedirectUri: Constants.expoConfig?.extra?.oktaLogoutRedirectUri || defaultEnvValues.oktaLogoutRedirectUri,
    environment: currentEnv,
    dcApiBaseUrl: Constants.expoConfig?.extra?.dcApiBaseUrl || defaultEnvValues.dcApiBaseUrl,
    dcApiKey: Constants.expoConfig?.extra?.dcApiKey || '',
  };
};

// Export the environment configuration
export const env = getEnvVars();

export default env;
