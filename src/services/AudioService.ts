/**
 * Audio Service
 * 
 * This service handles audio playback functionality.
 */

import { Audio, AVPlaybackStatus } from 'expo-av';
import { Sound } from 'expo-av/build/Audio';

class AudioService {
  private sound: Sound | null = null;
  private isPlaying: boolean = false;

  /**
   * Load an audio file
   * @param source The URI of the audio file
   */
  public async loadAudio(source: string): Promise<void> {
    try {
      // Unload any existing sound
      await this.unloadAudio();

      // Load the new sound
      const { sound } = await Audio.Sound.createAsync(
        { uri: source },
        { shouldPlay: false }
      );

      this.sound = sound;
      
      // Set up status update callback
      sound.setOnPlaybackStatusUpdate(this.onPlaybackStatusUpdate);
      
      console.log('Audio loaded successfully');
    } catch (error) {
      console.error('Error loading audio:', error);
      throw error;
    }
  }

  /**
   * Play the loaded audio
   */
  public async playAudio(): Promise<void> {
    if (!this.sound) {
      throw new Error('No audio loaded');
    }

    try {
      await this.sound.playAsync();
      this.isPlaying = true;
    } catch (error) {
      console.error('Error playing audio:', error);
      throw error;
    }
  }

  /**
   * Pause the currently playing audio
   */
  public async pauseAudio(): Promise<void> {
    if (!this.sound) {
      return;
    }

    try {
      await this.sound.pauseAsync();
      this.isPlaying = false;
    } catch (error) {
      console.error('Error pausing audio:', error);
      throw error;
    }
  }

  /**
   * Stop the currently playing audio
   */
  public async stopAudio(): Promise<void> {
    if (!this.sound) {
      return;
    }

    try {
      await this.sound.stopAsync();
      this.isPlaying = false;
    } catch (error) {
      console.error('Error stopping audio:', error);
      throw error;
    }
  }

  /**
   * Unload the audio
   */
  public async unloadAudio(): Promise<void> {
    if (!this.sound) {
      return;
    }

    try {
      await this.sound.unloadAsync();
      this.sound = null;
      this.isPlaying = false;
    } catch (error) {
      console.error('Error unloading audio:', error);
      throw error;
    }
  }

  /**
   * Check if audio is currently playing
   */
  public isAudioPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Handle playback status updates
   */
  private onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if (!status.isLoaded) {
      // Update state if there was an error
      if (status.error) {
        console.error(`Error: ${status.error}`);
      }
      return;
    }

    // Update playing state
    this.isPlaying = status.isPlaying;

    // Handle playback completion
    if (status.didJustFinish) {
      this.isPlaying = false;
    }
  };
}

// Export a singleton instance
export const audioService = new AudioService();
