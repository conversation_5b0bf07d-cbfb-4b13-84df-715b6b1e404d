/**
 * Bluetooth Service
 *
 * This service handles Bluetooth connectivity and device management.
 */

import { Platform } from 'react-native';
import { BleManager, Device, State } from 'react-native-ble-plx';

// Define the device type
export interface BluetoothDevice {
  id: string;
  name: string | null;
  isConnected: boolean;
}

// Define the service class
class BluetoothService {
  private manager: BleManager;
  private devices: Map<string, BluetoothDevice>;
  private connectedDevice: BluetoothDevice | null;

  constructor() {
    this.manager = new BleManager();
    this.devices = new Map();
    this.connectedDevice = null;
  }

  /**
   * Check if Bluetooth is enabled
   */
  public async isBluetoothEnabled(): Promise<boolean> {
    try {
      const state = await this.manager.state();
      return state === State.PoweredOn;
    } catch (error) {
      console.error('Error checking Bluetooth state:', error);
      return false;
    }
  }

  /**
   * Request Bluetooth permissions
   */
  public async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      try {
        // iOS handles permissions through info.plist
        // Just check if Bluetooth is enabled
        const state = await this.manager.state();
        return state === State.PoweredOn;
      } catch (error) {
        console.error('Error checking Bluetooth state on iOS:', error);
        return false;
      }
    }

    if (Platform.OS === 'android') {
      try {
        // For Android, we need to request permissions explicitly
        // This is a simplified approach - in a real app, you would use
        // react-native-permissions to request the permissions properly

        // Check if Bluetooth is enabled
        const state = await this.manager.state();
        if (state !== State.PoweredOn) {
          console.log('Bluetooth is not enabled, requesting user to enable it');
          // We can't programmatically enable Bluetooth, so we'll just inform the user
          return false;
        }

        return true;
      } catch (error) {
        console.error('Error requesting Bluetooth permissions:', error);
        return false;
      }
    }

    return false;
  }

  /**
   * Start scanning for Bluetooth devices
   * @param onDeviceFound Callback when a device is found
   */
  public async startScan(onDeviceFound: (device: BluetoothDevice) => void): Promise<void> {
    try {
      // Clear previous devices
      this.devices.clear();

      // Start scanning
      this.manager.startDeviceScan(null, null, (error, device) => {
        if (error) {
          console.error('Error scanning for devices:', error);
          return;
        }

        if (device && this.isAudioDevice(device)) {
          const bluetoothDevice: BluetoothDevice = {
            id: device.id,
            name: device.name || 'Unknown Device',
            isConnected: false,
          };

          // Add to devices map if not already present
          if (!this.devices.has(device.id)) {
            this.devices.set(device.id, bluetoothDevice);
            onDeviceFound(bluetoothDevice);
          }
        }
      });
    } catch (error) {
      console.error('Error starting scan:', error);
      throw error;
    }
  }

  /**
   * Stop scanning for Bluetooth devices
   */
  public stopScan(): void {
    this.manager.stopDeviceScan();
  }

  /**
   * Connect to a Bluetooth device
   * @param deviceId The device ID to connect to
   */
  public async connectToDevice(deviceId: string): Promise<BluetoothDevice> {
    try {
      // Stop scanning
      this.stopScan();

      // Connect to the device
      const device = await this.manager.connectToDevice(deviceId);

      // Discover services and characteristics
      await device.discoverAllServicesAndCharacteristics();

      // Update device in map
      const bluetoothDevice: BluetoothDevice = {
        id: device.id,
        name: device.name || 'Unknown Device',
        isConnected: true,
      };

      this.devices.set(device.id, bluetoothDevice);
      this.connectedDevice = bluetoothDevice;

      return bluetoothDevice;
    } catch (error) {
      console.error('Error connecting to device:', error);
      throw error;
    }
  }

  /**
   * Disconnect from a Bluetooth device
   * @param deviceId The device ID to disconnect from
   */
  public async disconnectFromDevice(deviceId: string): Promise<void> {
    try {
      await this.manager.cancelDeviceConnection(deviceId);

      // Update device in map
      const device = this.devices.get(deviceId);
      if (device) {
        device.isConnected = false;
        this.devices.set(deviceId, device);
      }

      this.connectedDevice = null;
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      throw error;
    }
  }

  /**
   * Get all discovered devices
   */
  public getDevices(): BluetoothDevice[] {
    return Array.from(this.devices.values());
  }

  /**
   * Get the currently connected device
   */
  public getConnectedDevice(): BluetoothDevice | null {
    return this.connectedDevice;
  }

  /**
   * Check if a device is an audio device
   * This is a simple check based on the device name
   * In a real app, you would check the device's services
   */
  private isAudioDevice(device: Device): boolean {
    const name = device.name?.toLowerCase() || '';
    return (
      name.includes('headphone') ||
      name.includes('speaker') ||
      name.includes('audio') ||
      name.includes('sound') ||
      name.includes('earphone') ||
      name.includes('earbud') ||
      name.includes('headset') ||
      // Common Bluetooth audio device brands
      name.includes('bose') ||
      name.includes('sony') ||
      name.includes('beats') ||
      name.includes('airpod') ||
      name.includes('jabra') ||
      name.includes('jbl') ||
      name.includes('samsung') ||
      name.includes('sennheiser')
    );
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    this.manager.destroy();
  }
}

// Export a singleton instance
export const bluetoothService = new BluetoothService();
