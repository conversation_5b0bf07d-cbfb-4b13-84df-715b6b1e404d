import { Stack, useRouter } from "expo-router";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import ArrowLeftIcon from "@/assets/icons/eneco/arrow/ArrowLeftIcon.svg";
import { tokens, useAppTheme } from "@/src/design-system";
import { Heading, Paragraph } from "@/src/design-system/components";

export default function ConsumptionInfoScreen() {
  const router = useRouter();
  useAppTheme();

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        {/* Header with back button */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeftIcon
              width={24}
              height={24}
              fill={tokens.colors.textColors.textPrimary}
            />
          </TouchableOpacity>
          <Heading
            as="h1"
            size="M"
          >
            The consumption for this month
          </Heading>
        </View>

        {/* Content */}
        <View style={styles.content}>
          <Paragraph style={styles.paragraph}>
            The consumption for this month and the corresponding variable costs are displayed here. The consumption costs do not include fixed delivery costs, grid management costs or energy tax reductions.
          </Paragraph>

          <Paragraph style={[styles.paragraph, styles.paragraphSpacing]}>
            Your meter readings are supplied to us by your grid manager. In general, there is a two-day interval between the consumption amount displayed here and the amount displayed on your smart meter. The information on the annual bill is leading.
          </Paragraph>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary
  },
  container: {
    flex: 1,
    padding: tokens.spacing.space[4],
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: tokens.spacing.space[6],
  },
  backButton: {
    marginRight: tokens.spacing.space[4],
  },

  content: {
    flex: 1,
  },
  paragraph: {
    color: tokens.colors.textColors.textPrimary,
    fontSize: tokens.typography.fontSizes.BodyM,
    lineHeight: tokens.typography.lineHeights[7],
    fontFamily: tokens.typography.fontFamilies.base,
  },
  paragraphSpacing: {
    marginTop: tokens.spacing.space[6],
  },
});
