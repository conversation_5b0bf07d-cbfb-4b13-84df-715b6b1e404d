import { useAuth } from "@/src/auth/AuthContext";
import { tokens } from "@/src/design-system";
import { Heading, Paragraph } from "@/src/design-system/components";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function CallbackScreen() {
  const { authState, isLoading } = useAuth();
  const router = useRouter();

  // Redirect to the main app if authenticated
  useEffect(() => {
    if (!isLoading && authState.isAuthenticated) {
      router.replace("/(tabs)");
    }
  }, [authState.isAuthenticated, isLoading, router]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Heading as="h1" size="XS" style={styles.heading}>
          Signing in...
        </Heading>
        <ActivityIndicator
          size="large"
          color={tokens.colors.secondaryColors.accentGreen700}
          style={styles.loader}
        />
        <Paragraph style={styles.text}>
          Please wait while we complete the authentication process.
        </Paragraph>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: tokens.spacing.space[4],
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
    textAlign: "center",
  },
  loader: {
    marginVertical: tokens.spacing.space[4],
  },
  text: {
    textAlign: "center",
    maxWidth: 300,
  },
});
