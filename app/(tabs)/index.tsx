import { TabLayout } from "@/src/components/layout/TabLayout";
import { tokens, useAppTheme } from "@/src/design-system";
import { Card, Heading, Paragraph, Text } from "@/src/design-system/components";
import { useRouter } from "expo-router";
import { StyleSheet, TouchableOpacity, View } from "react-native";

import CheckIcon from "@/assets/icons/eneco/CheckIcon.svg";
import CloseIcon from "@/assets/icons/eneco/CloseIcon.svg";
import DocumentIcon from "@/assets/icons/eneco/DocumentIcon.svg";
import HomeIcon from "@/assets/icons/eneco/HomeIcon.svg";
import SmileIcon from "@/assets/icons/eneco/SmileIcon.svg";
import StandbyConsumptionIcon from "@/assets/icons/eneco/StandbyConsumptionIcon.svg";
import ChevronRightIcon from "@/assets/icons/eneco/arrow/ChevronRightIcon.svg";
import ElectricityIcon from "@/assets/icons/eneco/product/ElectricityIcon.svg";
import WarmthIcon from "@/assets/icons/eneco/product/WarmthIcon.svg";
import InfoIcon from "@/assets/icons/eneco/status/InfoIcon.svg";

import EnecoLogo from "@/assets/images/eneco.svg";

// Styles
const styles = StyleSheet.create({
  logo: {
    marginBottom: tokens.spacing.space[4],
  },
  card: {
    marginBottom: tokens.spacing.space[4],
  },
  returnPaymentCard: {
    backgroundColor: tokens.colors.secondaryColors.accentGreen700,
    padding: tokens.spacing.space[4],
    borderRadius: tokens.borders.radii.m,
    flexDirection: "row",
    alignItems: "center",
  },
  returnPaymentText: {
    color: tokens.colors.neutralColors.neutralWhite,
    flex: 1,
    marginRight: tokens.spacing.space[2],
  },
  usageCard: {
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    borderRadius: tokens.borders.radii.m,
    padding: tokens.spacing.space[4],
    marginBottom: tokens.spacing.space[4],
  },
  usageHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: tokens.spacing.space[2],
  },
  usageItem: {
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
    borderRadius: tokens.borders.radii.m,
    padding: tokens.spacing.space[4],
    marginTop: tokens.spacing.space[2],
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  usageItemContent: {
    flex: 1,
  },
  dailyUsageGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: tokens.spacing.space[4],
    gap: tokens.spacing.space[2],
  },
  dailyUsageCard: {
    flex: 1,
    minWidth: "48%",
    padding: tokens.spacing.space[4],
    borderRadius: tokens.borders.radii.m,
    alignItems: "center",
    justifyContent: "center",
  },
  actionButtonsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: tokens.spacing.space[2],
    marginBottom: tokens.spacing.space[12],
  },
  actionButton: {
    flex: 1,
    minWidth: "48%",
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
    padding: tokens.spacing.space[4],
    borderRadius: tokens.borders.radii.m,
    alignItems: "center",
    justifyContent: "center",
  },
  notificationBanner: {
    backgroundColor: tokens.colors.secondaryColors.blue700,
    padding: tokens.spacing.space[4],
    borderRadius: tokens.borders.radii.m,
    marginTop: tokens.spacing.space[4],
    marginBottom: tokens.spacing.space[4],
    flexDirection: "row",
  },
  notificationText: {
    color: tokens.colors.neutralColors.neutralWhite,
    flex: 1,
  },
  closeButton: {
    marginLeft: tokens.spacing.space[2],
  },
  iconContainer: {
    marginRight: tokens.spacing.space[2],
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: tokens.colors.secondaryColors.accentGreen100,
    alignItems: "center",
    justifyContent: "center",
    marginRight: tokens.spacing.space[2],
  },
  chevronIcon: {
    marginLeft: tokens.spacing.space[2],
  },
});

export default function DashboardScreen() {
  useAppTheme();
  const router = useRouter();
  const currentDate = new Date();
  const dayOfWeek = currentDate.toLocaleString("en-US", { weekday: "short" });
  const formattedDate = `${currentDate.getDate()} May`;

  return (
    <TabLayout backgroundColor={tokens.colors.backgroundColors.backgroundSecondary} scrollable>
          {/* Eneco Logo */}
          <View style={styles.logo}>
            <EnecoLogo style={{ height: 48 }} />
          </View>

          {/* Return Payment Card */}
          <TouchableOpacity>
            <View style={styles.returnPaymentCard}>
              <View style={styles.iconCircle}>
                <CheckIcon
                  width={24}
                  height={24}
                  fill={tokens.colors.secondaryColors.accentGreen700}
                />
              </View>
              <View style={styles.returnPaymentText}>
                <Text color={tokens.colors.neutralColors.neutralWhite}>
                  We estimate an € 50,00 - € 75,00 return payment
                </Text>
              </View>

              <ChevronRightIcon
                width={24}
                height={24}
                fill={tokens.colors.neutralColors.neutralWhite}
              />
            </View>
          </TouchableOpacity>

          {/* Usage This Month */}
          <Card style={styles.card} corners="rounded" elevation="S">
            <Card.Content>
              <View style={styles.usageHeader}>
                <Heading as="h3" size="XS">
                  Usage this month
                </Heading>
                <TouchableOpacity
                  onPress={() => router.push("/consumption-info")}
                >
                  <InfoIcon
                    width={24}
                    height={24}
                    fill={tokens.colors.neutralColors.neutral800}
                  />
                </TouchableOpacity>
              </View>
              <Paragraph>Your usage from 1 - 18 May</Paragraph>

              {/* Electricity Usage */}
              <TouchableOpacity>
                <View style={styles.usageItem}>
                  <ElectricityIcon
                    width={24}
                    height={24}
                    fill={tokens.colors.secondaryColors.accentGreen700}
                    style={styles.iconContainer}
                  />
                  <View style={styles.usageItemContent}>
                    <Heading as="h4" size="S">
                      Electricity
                    </Heading>
                  </View>
                  <View>
                    <Heading as="h4" size="S">
                      € 25,62
                    </Heading>
                    <Text style={{ textAlign: "right" }}>100 kWh</Text>
                  </View>
                  <ChevronRightIcon
                    width={24}
                    height={24}
                    fill={tokens.colors.neutralColors.neutral800}
                  />
                </View>
              </TouchableOpacity>

              {/* Heat Usage */}
              <TouchableOpacity>
                <View style={styles.usageItem}>
                  <WarmthIcon
                    width={24}
                    height={24}
                    fill={tokens.colors.secondaryColors.pink700}
                    style={styles.iconContainer}
                  />

                  <View style={styles.usageItemContent}>
                    <Heading as="h4" size="S">
                      Heat
                    </Heading>
                  </View>
                  <View>
                    <Heading as="h4" size="S">
                      € 44,76
                    </Heading>
                    <Text style={{ textAlign: "right" }}>0.27 GJ</Text>
                  </View>

                  <ChevronRightIcon
                    width={24}
                    height={24}
                    fill={tokens.colors.neutralColors.neutral800}
                    style={styles.chevronIcon}
                  />
                </View>
              </TouchableOpacity>
            </Card.Content>
          </Card>

          {/* Daily Usage Cards */}
          <View style={styles.dailyUsageGrid}>
            {/* Electricity Daily Usage */}
            <TouchableOpacity
              style={[
                styles.dailyUsageCard,
                {
                  backgroundColor: tokens.colors.secondaryColors.accentGreen100,
                },
              ]}
            >
              <Text>
                {dayOfWeek} {formattedDate}
              </Text>
              <ElectricityIcon
                width={24}
                height={24}
                fill={tokens.colors.secondaryColors.accentGreen700}
                style={{ marginVertical: tokens.spacing.space[2] }}
              />
              <Heading as="h3" size="L">
                € 0,98
              </Heading>
            </TouchableOpacity>

            {/* Energy Return Daily Usage */}
            <TouchableOpacity
              style={[
                styles.dailyUsageCard,
                { backgroundColor: tokens.colors.secondaryColors.yellow100 },
              ]}
            >
              <Text>
                {dayOfWeek} {formattedDate}
              </Text>
              <ElectricityIcon
                width={24}
                height={24}
                fill={tokens.colors.secondaryColors.yellow700}
                style={{ marginVertical: tokens.spacing.space[2] }}
              />
              <Heading as="h3" size="L">
                € 0,98
              </Heading>
            </TouchableOpacity>

            {/* Heat Daily Usage */}
            <TouchableOpacity
              style={[
                styles.dailyUsageCard,
                { backgroundColor: tokens.colors.secondaryColors.pink100 },
              ]}
            >
              <Text>
                {dayOfWeek} {formattedDate}
              </Text>
              <WarmthIcon
                width={24}
                height={24}
                fill={tokens.colors.secondaryColors.pink700}
                style={{ marginVertical: tokens.spacing.space[2] }}
              />
              <Heading as="h3" size="L">
                € 2,95
              </Heading>
            </TouchableOpacity>
          </View>

          {/* Notification Banner */}
          <View style={styles.notificationBanner}>
            <View style={styles.notificationText}>
              <Heading
                as="h4"
                size="M"
                style={{ color: tokens.colors.neutralColors.neutralWhite }}
              >
                Stay informed!
              </Heading>
              <Text style={{ color: tokens.colors.neutralColors.neutralWhite }}>
                The energy market is changing. We are happy to inform you with
                our email newsletter, information and advice.
              </Text>
              <TouchableOpacity style={{ marginTop: tokens.spacing.space[2] }}>
                <Text style={{ color: tokens.colors.secondaryColors.pink500 }}>
                  Change contact preferences
                </Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity style={styles.closeButton}>
              <CloseIcon
                width={24}
                height={24}
                fill={tokens.colors.neutralColors.neutralWhite}
              />
            </TouchableOpacity>
          </View>

          {/* Go To Section */}
          <Heading
            as="h3"
            size="M"
            style={{ marginBottom: tokens.spacing.space[2] }}
          >
            Go to
          </Heading>

          <View style={styles.actionButtonsGrid}>
            {/* Bills Button */}
            <TouchableOpacity style={styles.actionButton}>
              <Heading as="h4" size="XS">
                Bills
              </Heading>
              <DocumentIcon
                width={32}
                height={32}
                fill={tokens.colors.neutralColors.neutral800}
                style={{ marginTop: tokens.spacing.space[2] }}
              />
            </TouchableOpacity>

            {/* Products Button */}
            <TouchableOpacity style={styles.actionButton}>
              <Heading as="h4" size="XS">
                Products
              </Heading>
              <HomeIcon
                width={32}
                height={32}
                fill={tokens.colors.neutralColors.neutral800}
                style={{ marginTop: tokens.spacing.space[2] }}
              />
            </TouchableOpacity>

            {/* Interim Reading Button */}
            <TouchableOpacity style={styles.actionButton}>
              <Heading as="h4" size="XS">
                Interim reading
              </Heading>
              <SmileIcon
                width={32}
                height={32}
                fill={tokens.colors.neutralColors.neutral800}
                style={{ marginTop: tokens.spacing.space[2] }}
              />
            </TouchableOpacity>

            {/* Standby Button */}
            <TouchableOpacity style={styles.actionButton}>
              <Heading as="h4" size="XS">
                Standby
              </Heading>
              <Text>30 days</Text>
              <StandbyConsumptionIcon
                width={32}
                height={32}
                fill={tokens.colors.neutralColors.neutral800}
                style={{ marginTop: tokens.spacing.space[2] }}
              />
            </TouchableOpacity>
          </View>
    </TabLayout>
  );
}
