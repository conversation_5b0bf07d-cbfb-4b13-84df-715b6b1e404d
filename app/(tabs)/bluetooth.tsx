/**
 * Bluetooth Audio Demo Screen
 */

import { BluetoothAudioDemo } from '@/src/components/BluetoothAudioDemo';
import { TabLayout } from "@/src/components/layout/TabLayout";
import { useAppTheme } from '@/src/design-system';
import React from 'react';
import { StatusBar } from 'react-native';

/**
 * Bluetooth Audio Demo Screen
 * Fixed: Removed ScrollView to prevent VirtualizedList nesting warning
 */
export default function BluetoothScreen() {
  const { theme } = useAppTheme();

  return (
    <TabLayout scrollable={false} contentPadding={false}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />
      <BluetoothAudioDemo />
    </TabLayout>
  );
}
