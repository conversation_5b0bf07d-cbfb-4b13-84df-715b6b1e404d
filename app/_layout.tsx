import { useFonts } from "expo-font";
import { St<PERSON>, useRouter, useSegments } from "expo-router";
import { StatusBar } from "expo-status-bar";
import * as WebBrowser from 'expo-web-browser';
import { useEffect } from "react";
import "react-native-reanimated";

import { AuthProvider, useAuth } from "@/src/auth/AuthContext";
import { initializeAuth } from "@/src/auth/OktaService";
import { ThemeProvider } from "@/src/design-system";

// Initialize WebBrowser and Auth Session
WebBrowser.maybeCompleteAuthSession();
initializeAuth();

// Auth navigation guard component
function AuthGuard({ children }: { children: React.ReactNode }) {
  const { authState, isLoading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    const inAuthGroup = segments[0] === "(tabs)";
    const isCallbackRoute = segments[0] === "callback" ||
                           (segments[0] === "api" && segments[1] === "auth" && segments[2] === "callback");
    const isLoginRoute = segments[0] === "login";

    if (!authState.isAuthenticated && inAuthGroup) {
      // Redirect to the login page if not authenticated
      router.replace("/login");
    } else if (authState.isAuthenticated && !inAuthGroup && !isCallbackRoute) {
      // Redirect to the main app if authenticated and not on the callback route
      router.replace("/(tabs)");
    }
  }, [authState.isAuthenticated, segments, isLoading, router]);

  return <>{children}</>;
}

const RootLayout = () => {
  const [loaded] = useFonts({
    Etelka: require("../assets/fonts/etelkaMedium.ttf"),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <AuthGuard>
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="consumption-info" options={{ headerShown: false }} />
            <Stack.Screen name="login" options={{ headerShown: false }} />
            <Stack.Screen name="callback" options={{ headerShown: false }} />
            <Stack.Screen name="api/auth/callback" options={{ headerShown: false }} />
            <Stack.Screen name="+not-found" />
          </Stack>
        </AuthGuard>
      </AuthProvider>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export default RootLayout;