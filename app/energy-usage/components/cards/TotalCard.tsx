/**
 * Total Energy Usage Card Component
 */

import {
  Card,
  Text,
  tokens,
  useAppTheme
} from '@/src/design-system';
import React from 'react';
import { StyleSheet } from 'react-native';
import { EnergySummary, TimePeriod } from '../../types/api';
import { formatCurrency, formatDateRange } from '../../utils/formatters';


interface TotalCardProps {
  summaryData: EnergySummary | null;
  period: TimePeriod;
  isLoading?: boolean;
}

/**
 * Total Energy Usage Card Component
 * Displays the total energy cost for the selected period
 */
export const TotalCard: React.FC<TotalCardProps> = ({
  summaryData,
  period,
  isLoading = false,
}) => {
  const { theme } = useAppTheme();
  
  if (isLoading) {
    return (
      <Card elevation="S" style={styles.card}>
        <Card.Content>
          <Text>Loading...</Text>
        </Card.Content>
      </Card>
    );
  }
  
  if (!summaryData) {
    return (
      <Card elevation="S" style={styles.card}>
        <Card.Content>
          <Text>No data available</Text>
        </Card.Content>
      </Card>
    );
  }
  
  const { from, to } = summaryData.period;
  const dateRangeText = formatDateRange(from, to, period);
  
  return (
    <Card elevation="S" style={styles.card}>
      <Card.Content>
        <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
          Total Energy Cost
        </Text>
        <Text variant="headlineLarge" style={{ color: theme.colors.onSurface, marginTop: 4 }}>
          {formatCurrency(summaryData.total.cost)}
        </Text>
        <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
          {dateRangeText}
        </Text>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: tokens.spacing.space[3],
  },
});

export default TotalCard;
