/**
 * Energy Breakdown Card Component
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import Card from '../../../../src/design-system/components/Card';
import { Text } from '../../../../src/design-system/components/Text';
import { useAppTheme } from '../../../../src/design-system/provider';
import { EnergySummary } from '../../types/api';
import { formatConsumption, formatCurrency } from '../../utils/formatters';

interface BreakdownCardProps {
  summaryData: EnergySummary | null;
  isLoading?: boolean;
}

/**
 * Energy Breakdown Card Component
 * Displays a detailed breakdown of electricity and gas usage with costs
 */
export const BreakdownCard: React.FC<BreakdownCardProps> = ({
  summaryData,
  isLoading = false,
}) => {
  const { theme } = useAppTheme();
  
  if (isLoading) {
    return (
      <Card elevation="S" style={styles.card}>
        <Card.Content>
          <Text>Loading...</Text>
        </Card.Content>
      </Card>
    );
  }
  
  if (!summaryData) {
    return (
      <Card elevation="S" style={styles.card}>
        <Card.Content>
          <Text>No data available</Text>
        </Card.Content>
      </Card>
    );
  }
  
  return (
    <Card elevation="S" style={styles.card}>
      <Card.Title title="Energy Breakdown" />
      <Card.Content>
        <View style={styles.breakdownContainer}>
          {/* Electricity Section */}
          <View style={styles.energyTypeContainer}>
            <View style={styles.headerRow}>
              <View style={[styles.colorIndicator, { backgroundColor: theme.colors.primary }]} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                Electricity
              </Text>
            </View>
            
            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Usage:
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                  {formatConsumption(summaryData.electricity.consumption, 'electricity')}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Cost:
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                  {formatCurrency(summaryData.electricity.cost)}
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.divider} />
          
          {/* Gas Section */}
          <View style={styles.energyTypeContainer}>
            <View style={styles.headerRow}>
              <View style={[styles.colorIndicator, { backgroundColor: theme.colors.tertiary }]} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                Gas
              </Text>
            </View>
            
            <View style={styles.detailsContainer}>
              <View style={styles.detailRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Usage:
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                  {formatConsumption(summaryData.gas.consumption, 'gas')}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Cost:
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                  {formatCurrency(summaryData.gas.cost)}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  breakdownContainer: {
    width: '100%',
  },
  energyTypeContainer: {
    marginBottom: 12,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  detailsContainer: {
    marginLeft: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: 12,
  },
});

export default BreakdownCard;
