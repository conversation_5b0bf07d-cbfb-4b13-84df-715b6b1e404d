/**
 * Weather Information Card Component
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import Card from '../../../../src/design-system/components/Card';
import { Text } from '../../../../src/design-system/components/Text';
import { useAppTheme } from '../../../../src/design-system/provider';
import { EnergySummary } from '../../types/api';
import { formatDate, formatTemperature } from '../../utils/formatters';

interface WeatherCardProps {
  summaryData: EnergySummary | null;
  isLoading?: boolean;
}

/**
 * Weather Information Card Component
 * Displays weather information when available
 */
export const WeatherCard: React.FC<WeatherCardProps> = ({
  summaryData,
  isLoading = false,
}) => {
  const { theme } = useAppTheme();
  
  // Don't render if no weather data or loading
  if (isLoading || !summaryData || !summaryData.weather) {
    return null;
  }
  
  const { averageTemp, sunshine, coldestDay, warmestDay } = summaryData.weather;
  
  return (
    <Card elevation="S" style={styles.card}>
      <Card.Title title="Weather Information" />
      <Card.Content>
        <View style={styles.weatherContainer}>
          <View style={styles.mainWeatherInfo}>
            <Text variant="headlineMedium" style={{ color: theme.colors.onSurface }}>
              {formatTemperature(averageTemp)}
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Average Temperature
            </Text>
          </View>
          
          <View style={styles.sunshineContainer}>
            <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>
              {sunshine.toFixed(1)} hours
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Average Daily Sunshine
            </Text>
          </View>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.extremesContainer}>
          <View style={styles.extremeItem}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Coldest:
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
              {formatTemperature(coldestDay.temp)}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {formatDate(coldestDay.date, 'day')}
            </Text>
          </View>
          
          <View style={styles.extremeItem}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Warmest:
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
              {formatTemperature(warmestDay.temp)}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {formatDate(warmestDay.date, 'day')}
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  weatherContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  mainWeatherInfo: {
    flex: 1,
  },
  sunshineContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: 12,
  },
  extremesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  extremeItem: {
    flex: 1,
  },
});

export default WeatherCard;
