/**
 * Energy Saving Tips Card Component
 */

import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import Card from '../../../../src/design-system/components/Card';
import { Text } from '../../../../src/design-system/components/Text';
import { useAppTheme } from '../../../../src/design-system/provider';

interface TipsCardProps {
  tips: string[];
  isLoading?: boolean;
}

/**
 * Energy Saving Tips Card Component
 * Displays energy-saving tips based on usage patterns
 */
export const TipsCard: React.FC<TipsCardProps> = ({
  tips,
  isLoading = false,
}) => {
  const { theme } = useAppTheme();
  
  if (isLoading) {
    return (
      <Card elevation="S" style={styles.card}>
        <Card.Content>
          <Text>Loading tips...</Text>
        </Card.Content>
      </Card>
    );
  }
  
  if (!tips || tips.length === 0) {
    return null;
  }
  
  return (
    <Card elevation="S" style={styles.card}>
      <Card.Title title="Energy Saving Tips" />
      <Card.Content>
        <FlatList
          data={tips}
          keyExtractor={(item, index) => `tip-${index}`}
          scrollEnabled={false}
          renderItem={({ item, index }) => (
            <View style={styles.tipContainer}>
              <View style={[styles.tipBullet, { backgroundColor: theme.colors.primary }]} />
              <Text variant="bodyMedium" style={styles.tipText}>
                {item}
              </Text>
            </View>
          )}
        />
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  tipBullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 6,
    marginRight: 8,
  },
  tipText: {
    flex: 1,
  },
});

export default TipsCard;
