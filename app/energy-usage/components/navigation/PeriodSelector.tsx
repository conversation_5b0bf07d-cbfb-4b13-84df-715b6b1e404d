/**
 * Period Selector Component
 */

import {
  Text,
  tokens,
  useAppTheme
} from '@/src/design-system';
import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { TimePeriod } from '../../types/api';

interface PeriodSelectorProps {
  selectedPeriod: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
}

const periods: { label: string; value: TimePeriod }[] = [
  { label: 'Hour', value: 'hour' },
  { label: 'Day', value: 'day' },
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' },
  { label: 'Year', value: 'year' },
];

/**
 * Period Selector Component
 * Allows users to select different time periods (Hour, Day, Week, Month, Year)
 */
export const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  selectedPeriod,
  onPeriodChange,
}) => {
  const { theme } = useAppTheme();
  
  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
      >
        {periods.map((period) => (
          <TouchableOpacity
            key={period.value}
            style={[
              styles.periodButton,
              selectedPeriod === period.value && {
                backgroundColor: theme.colors.primaryContainer,
                borderColor: theme.colors.primary,
              },
            ]}
            onPress={() => onPeriodChange(period.value)}
          >
            <Text
              style={[
                styles.periodText,
                selectedPeriod === period.value && {
                  color: theme.colors.onPrimaryContainer,
                  fontWeight: 'bold',
                },
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
 
  periodButton: {
    paddingHorizontal: tokens.spacing.space[3],
    paddingVertical: tokens.spacing.space[2],
    borderRadius: tokens.borders.radii.l,
    marginRight: tokens.spacing.space[2],
    borderWidth: 1,
    borderColor: 'transparent',
  },
  periodText: {
    fontSize: tokens.typography.bodyTypography.bodyM.fontSize,
  },
});

export default PeriodSelector;
