/**
 * Chart Controls Component
 */

import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from '../../../../src/design-system/components/Text';
import { useAppTheme } from '../../../../src/design-system/provider';
import { DisplayMode } from '../../types/api';

interface ChartControlsProps {
  displayMode: DisplayMode;
  setDisplayMode: (mode: DisplayMode) => void;
  showComparison: boolean;
  setShowComparison: (show: boolean) => void;
}

/**
 * Chart Controls Component
 * Provides controls for toggling between cost and consumption display modes
 * and enabling/disabling year-over-year comparison
 */
export const ChartControls: React.FC<ChartControlsProps> = ({
  displayMode,
  setDisplayMode,
  showComparison,
  setShowComparison,
}) => {
  const { theme } = useAppTheme();
  
  return (
    <View style={styles.container}>
      <View style={styles.displayModeContainer}>
        <TouchableOpacity
          style={[
            styles.displayModeButton,
            displayMode === 'cost' && { backgroundColor: theme.colors.primaryContainer },
          ]}
          onPress={() => setDisplayMode('cost')}
        >
          <Text
            style={[
              styles.displayModeText,
              displayMode === 'cost' && { color: theme.colors.onPrimaryContainer },
            ]}
          >
            Cost (€)
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.displayModeButton,
            displayMode === 'consumption' && { backgroundColor: theme.colors.primaryContainer },
          ]}
          onPress={() => setDisplayMode('consumption')}
        >
          <Text
            style={[
              styles.displayModeText,
              displayMode === 'consumption' && { color: theme.colors.onPrimaryContainer },
            ]}
          >
            Consumption
          </Text>
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity
        style={[
          styles.comparisonButton,
          showComparison && { backgroundColor: theme.colors.secondaryContainer },
        ]}
        onPress={() => setShowComparison(!showComparison)}
      >
        <Text
          style={[
            styles.comparisonText,
            showComparison && { color: theme.colors.onSecondaryContainer },
          ]}
        >
          {showComparison ? 'Hide Comparison' : 'Show Last Year'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 10,
    paddingHorizontal: 10,
  },
  displayModeContainer: {
    flexDirection: 'row',
  },
  displayModeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  displayModeText: {
    fontSize: 14,
  },
  comparisonButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  comparisonText: {
    fontSize: 14,
  },
});

export default ChartControls;
