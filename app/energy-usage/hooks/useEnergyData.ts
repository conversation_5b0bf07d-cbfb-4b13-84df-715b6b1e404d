/**
 * Hook for fetching and processing energy usage data
 */

import { useEffect, useState } from 'react';
import { ChartDataPoint, DisplayMode, EnergyApiResponse, EnergySummary, TimePeriod } from '../types/api';
import { extractSummaryData, generateEnergySavingTips, transformApiDataToChartData } from '../utils/dataTransformers';
import { getDateRangeForPeriod } from '../utils/dateUtils';

// Mock API data for development
import mockApiResponse from '../mocks/energyApiResponse.json';

interface UseEnergyDataProps {
  period: TimePeriod;
  showComparison?: boolean;
}

interface UseEnergyDataResult {
  isLoading: boolean;
  error: Error | null;
  chartData: ChartDataPoint[];
  summaryData: EnergySummary | null;
  energySavingTips: string[];
  displayMode: DisplayMode;
  setDisplayMode: (mode: DisplayMode) => void;
  refreshData: () => void;
}

/**
 * Hook for fetching and processing energy usage data
 */
export const useEnergyData = ({
  period,
  showComparison = false,
}: UseEnergyDataProps): UseEnergyDataResult => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [apiResponse, setApiResponse] = useState<EnergyApiResponse | null>(null);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [summaryData, setSummaryData] = useState<EnergySummary | null>(null);
  const [energySavingTips, setEnergySavingTips] = useState<string[]>([]);
  const [displayMode, setDisplayMode] = useState<DisplayMode>('cost');

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // In a real app, this would be an API call
      // const dateRange = getDateRangeForPeriod(period);
      // const response = await fetch(`/api/energy-usage?from=${dateRange.from}&to=${dateRange.to}&period=${period}&comparison=${showComparison}`);
      // const data = await response.json();
      
      // For this implementation, we'll use mock data
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Use mock data
      const data = mockApiResponse as EnergyApiResponse;
      
      setApiResponse(data);
      
      // Transform data for charts
      const transformedChartData = transformApiDataToChartData(data, period);
      setChartData(transformedChartData);
      
      // Extract summary data
      const extractedSummaryData = extractSummaryData(data);
      setSummaryData(extractedSummaryData);
      
      // Generate energy saving tips
      const tips = generateEnergySavingTips(extractedSummaryData);
      setEnergySavingTips(tips);
      
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch energy data'));
      console.error('Error fetching energy data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when period or comparison changes
  useEffect(() => {
    fetchData();
  }, [period, showComparison]);

  return {
    isLoading,
    error,
    chartData,
    summaryData,
    energySavingTips,
    displayMode,
    setDisplayMode,
    refreshData: fetchData,
  };
};
