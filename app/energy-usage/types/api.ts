/**
 * TypeScript interfaces for the Energy Usage API
 */

// Energy API Response
export interface EnergyApiResponse {
  data: {
    metadata: {
      interval: string;
      aggregation: string;
    };
    usages: Array<{
      period: {
        from: string;
        to: string;
      };
      entries: Array<{
        actual: {
          date: string;
          gas?: {
            status: string;
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
            fixedCostInclVat: number;
          };
          electricity?: {
            status: string;
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
            fixedCostInclVat: number;
          };
          totalUsageCostInclVat: number;
          totalFixedCostInclVat: number;
          totalCostInclVat: number;
        };
        previousYear?: {
          date: string;
          gas?: {
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
          };
          electricity?: {
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
          };
          totalUsageCostInclVat: number;
          totalFixedCostInclVat: number;
          totalCostInclVat: number;
        };
        weather?: {
          date: string;
          temp: number;
          sunshine: number;
          coldestDay: {
            date: string;
            temp: number;
          };
          warmestDay: {
            date: string;
            temp: number;
          };
        };
      }>;
      summary: {
        aggregationTotals: {
          gas?: {
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
          };
          electricity?: {
            high: number;
            highCostInclVat: number;
            totalCostInclVat: number;
          };
        };
      };
    }>;
  };
}

// Chart data types
export interface ChartDataPoint {
  name: string;
  date: string;
  electricity?: number;
  gas?: number;
  electricityCost?: number;
  gasCost?: number;
  totalCost?: number;
  previousYearElectricity?: number;
  previousYearGas?: number;
  previousYearElectricityCost?: number;
  previousYearGasCost?: number;
  previousYearTotalCost?: number;
  weather?: {
    temp: number;
    sunshine: number;
  };
}

// Time period types
export type TimePeriod = 'hour' | 'day' | 'week' | 'month' | 'year';

// Display mode types
export type DisplayMode = 'cost' | 'consumption';

// Summary data type
export interface EnergySummary {
  period: {
    from: string;
    to: string;
  };
  electricity: {
    consumption: number;
    cost: number;
    unit: string;
  };
  gas: {
    consumption: number;
    cost: number;
    unit: string;
  };
  total: {
    cost: number;
  };
  weather?: {
    averageTemp: number;
    sunshine: number;
    coldestDay: {
      date: string;
      temp: number;
    };
    warmestDay: {
      date: string;
      temp: number;
    };
  };
}
