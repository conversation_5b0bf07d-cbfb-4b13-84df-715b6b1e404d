/**
 * Utility functions for formatting values in the Energy Usage Dashboard
 */

/**
 * Format a currency value in euros with commas for thousands
 * @param value The value to format
 * @param decimals The number of decimal places to include
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number, decimals = 2): string => {
  return new Intl.NumberFormat('nl-NL', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

/**
 * Format a number with commas for thousands
 * @param value The value to format
 * @param decimals The number of decimal places to include
 * @returns Formatted number string
 */
export const formatNumber = (value: number, decimals = 2): string => {
  return new Intl.NumberFormat('nl-NL', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

/**
 * Format a date string based on the time period
 * @param dateString The date string to format
 * @param period The time period
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, period: string): string => {
  const date = new Date(dateString);
  
  switch (period.toLowerCase()) {
    case 'hour':
      return new Intl.DateTimeFormat('nl-NL', {
        hour: 'numeric',
        minute: 'numeric',
      }).format(date);
    
    case 'day':
      return new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
      }).format(date);
    
    case 'week':
      return new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
      }).format(date);
    
    case 'month':
      return new Intl.DateTimeFormat('nl-NL', {
        month: 'short',
      }).format(date);
    
    case 'year':
      return new Intl.DateTimeFormat('nl-NL', {
        year: 'numeric',
      }).format(date);
    
    default:
      return new Intl.DateTimeFormat('nl-NL').format(date);
  }
};

/**
 * Format a temperature value
 * @param temp The temperature value
 * @returns Formatted temperature string
 */
export const formatTemperature = (temp: number): string => {
  return `${formatNumber(temp, 1)}°C`;
};

/**
 * Format a consumption value with the appropriate unit
 * @param value The consumption value
 * @param type The type of consumption ('electricity' or 'gas')
 * @returns Formatted consumption string with unit
 */
export const formatConsumption = (value: number, type: 'electricity' | 'gas'): string => {
  if (type === 'electricity') {
    return `${formatNumber(value, 1)} kWh`;
  } else {
    return `${formatNumber(value, 1)} m³`;
  }
};

/**
 * Format a date range for display
 * @param from Start date string
 * @param to End date string
 * @param period The time period
 * @returns Formatted date range string
 */
export const formatDateRange = (from: string, to: string, period: string): string => {
  const fromDate = new Date(from);
  const toDate = new Date(to);
  
  switch (period.toLowerCase()) {
    case 'hour':
      return new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
        hour: 'numeric',
        minute: 'numeric',
      }).format(fromDate);
    
    case 'day':
      return new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      }).format(fromDate);
    
    case 'week':
      return `${new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
      }).format(fromDate)} - ${new Intl.DateTimeFormat('nl-NL', {
        day: 'numeric',
        month: 'short',
      }).format(toDate)}`;
    
    case 'month':
      return new Intl.DateTimeFormat('nl-NL', {
        month: 'long',
        year: 'numeric',
      }).format(fromDate);
    
    case 'year':
      return new Intl.DateTimeFormat('nl-NL', {
        year: 'numeric',
      }).format(fromDate);
    
    default:
      return `${new Intl.DateTimeFormat('nl-NL').format(fromDate)} - ${new Intl.DateTimeFormat('nl-NL').format(toDate)}`;
  }
};
