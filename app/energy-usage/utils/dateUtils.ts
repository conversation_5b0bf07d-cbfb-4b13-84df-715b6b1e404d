/**
 * Utility functions for date handling in the Energy Usage Dashboard
 */

import { TimePeriod } from '../types/api';

/**
 * Get the start and end dates for a given time period
 * @param period The time period
 * @param referenceDate The reference date (defaults to current date)
 * @returns Object with from and to date strings
 */
export const getDateRangeForPeriod = (
  period: TimePeriod,
  referenceDate: Date = new Date()
): { from: string; to: string } => {
  const from = new Date(referenceDate);
  const to = new Date(referenceDate);

  switch (period) {
    case 'hour':
      // Current hour
      from.setMinutes(0, 0, 0);
      to.setMinutes(59, 59, 999);
      break;

    case 'day':
      // Current day
      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);
      break;

    case 'week':
      // Current week (Monday to Sunday)
      const day = from.getDay();
      const diff = from.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
      from.setDate(diff);
      from.setHours(0, 0, 0, 0);
      to.setDate(from.getDate() + 6);
      to.setHours(23, 59, 59, 999);
      break;

    case 'month':
      // Current month
      from.setDate(1);
      from.setHours(0, 0, 0, 0);
      to.setMonth(from.getMonth() + 1);
      to.setDate(0);
      to.setHours(23, 59, 59, 999);
      break;

    case 'year':
      // Current year
      from.setMonth(0, 1);
      from.setHours(0, 0, 0, 0);
      to.setMonth(11, 31);
      to.setHours(23, 59, 59, 999);
      break;

    default:
      // Default to day
      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);
  }

  return {
    from: from.toISOString(),
    to: to.toISOString(),
  };
};

/**
 * Get the previous period's date range
 * @param period The time period
 * @param currentFrom The current period's start date
 * @param currentTo The current period's end date
 * @returns Object with from and to date strings for the previous period
 */
export const getPreviousPeriodDateRange = (
  period: TimePeriod,
  currentFrom: string,
  currentTo: string
): { from: string; to: string } => {
  const fromDate = new Date(currentFrom);
  const toDate = new Date(currentTo);
  
  switch (period) {
    case 'hour':
      // Previous hour
      fromDate.setHours(fromDate.getHours() - 1);
      toDate.setHours(toDate.getHours() - 1);
      break;

    case 'day':
      // Previous day
      fromDate.setDate(fromDate.getDate() - 1);
      toDate.setDate(toDate.getDate() - 1);
      break;

    case 'week':
      // Previous week
      fromDate.setDate(fromDate.getDate() - 7);
      toDate.setDate(toDate.getDate() - 7);
      break;

    case 'month':
      // Previous month
      fromDate.setMonth(fromDate.getMonth() - 1);
      toDate.setMonth(toDate.getMonth() - 1);
      // Adjust the day if necessary (e.g., Jan 31 -> Feb 28)
      if (toDate.getDate() !== new Date(currentTo).getDate()) {
        toDate.setDate(0); // Set to the last day of the previous month
      }
      break;

    case 'year':
      // Previous year
      fromDate.setFullYear(fromDate.getFullYear() - 1);
      toDate.setFullYear(toDate.getFullYear() - 1);
      break;

    default:
      // Default to day
      fromDate.setDate(fromDate.getDate() - 1);
      toDate.setDate(toDate.getDate() - 1);
  }

  return {
    from: fromDate.toISOString(),
    to: toDate.toISOString(),
  };
};

/**
 * Get the previous year's date range
 * @param currentFrom The current period's start date
 * @param currentTo The current period's end date
 * @returns Object with from and to date strings for the same period in the previous year
 */
export const getPreviousYearDateRange = (
  currentFrom: string,
  currentTo: string
): { from: string; to: string } => {
  const fromDate = new Date(currentFrom);
  const toDate = new Date(currentTo);
  
  fromDate.setFullYear(fromDate.getFullYear() - 1);
  toDate.setFullYear(toDate.getFullYear() - 1);
  
  return {
    from: fromDate.toISOString(),
    to: toDate.toISOString(),
  };
};
