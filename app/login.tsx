import { useAuth } from "@/src/auth/AuthContext";
import { tokens, useAppTheme } from "@/src/design-system";
import { <PERSON><PERSON>, Heading } from "@/src/design-system/components";
import * as WebBrowser from "expo-web-browser";
import React, { useEffect } from "react";
import {
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Import SVG
import EnecoLogo from "@/assets/images/eneco.svg";

const Login = () => {
  const { theme } = useAppTheme();
  const { login, isLoading } = useAuth();

  // Warm up the browser when the component mounts
  useEffect(() => {
    if (Platform.OS !== "web") {
      WebBrowser.warmUpAsync();

      return () => {
        WebBrowser.coolDownAsync();
      };
    }
  }, []);

  const handleLogin = async () => {
    try {
      console.log("Login button pressed");
      await login();
    } catch (error) {
      console.error("Login error:", error);
      // Check if the error is about the request not being ready
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Authentication failed. Please try again.";
      Alert.alert("Login Failed", errorMessage);
    }
  };

  const handleCreateAccount = () => {
    // This is a dummy function for now
    Alert.alert("Create Account", "This feature is not implemented yet.");
  };

  const handleSettings = () => {
    // This is a dummy function for now
    Alert.alert("Settings", "This feature is not implemented yet.");
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Hero Image */}
          <View style={styles.imageContainer}>
            <Image
              source={require("@/assets/images/stock/woman-with-phone.png")}
              style={styles.heroImage}
              resizeMode="cover"
            />
          </View>

          {/* Welcome Content */}
          <View style={styles.formContainer}>
            <View style={styles.logoContainer}>
              <EnecoLogo width={120} height={60} />
            </View>
            <Heading as="h1" size="XS" style={styles.heading}>
              Welcome to Eneco
            </Heading>

            <View style={styles.buttonsContainer}>
              <Button
                action="primary"
                onPress={handleLogin}
                disabled={isLoading}
                isLoading={isLoading}
                style={styles.button}
              >
                Log In
              </Button>

              <Button
                action="secondary"
                onPress={handleCreateAccount}
                style={styles.button}
              >
                Create an Account
              </Button>

              <Button
                action="primary"
                tone="onColor"
                onPress={handleSettings}
                style={styles.button}
              >
                Settings
              </Button>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundPrimary,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: tokens.spacing.space[6],
  },
  logoContainer: {
    alignItems: "center",
    marginTop: tokens.spacing.space[4],
    marginBottom: tokens.spacing.space[2],
  },
  imageContainer: {
    backgroundColor: tokens.colors.secondaryColors.blueGray300,
    alignItems: "center",
    marginBottom: tokens.spacing.space[4],
  },
  heroImage: {
    width: "100%",
    height: 350,
    borderRadius: tokens.borders.radii.m,
  },
  formContainer: {
    paddingHorizontal: tokens.spacing.space[4],
  },
  heading: {
    textAlign: "center",
  },
  subheading: {
    marginBottom: tokens.spacing.space[4],
    textAlign: "center",
  },
  buttonsContainer: {
    alignItems: "center",
    gap: tokens.spacing.space[3],
    marginTop: tokens.spacing.space[4],
  },
  button: {
    width: "100%",
    maxWidth: 300,
  },
  inputContainer: {
    marginBottom: tokens.spacing.space[3],
  },
  label: {
    marginBottom: tokens.spacing.space[1],
    fontFamily: tokens.typography.fontFamilies.base,
    fontWeight: "500", // Using "500" directly instead of tokens.typography.fontWeights.medium
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: tokens.borders.radii.s,
    paddingHorizontal: tokens.spacing.space[3],
    fontFamily: tokens.typography.fontFamilies.base,
    fontSize: tokens.typography.fontSizes.BodyM,
  },
  forgotPassword: {
    alignSelf: "flex-end",
    marginBottom: tokens.spacing.space[4],
  },
  loginButton: {
    marginTop: tokens.spacing.space[2],
    width: "100%",
  },
  backButton: {
    marginTop: tokens.spacing.space[4],
    alignItems: "center",
    alignSelf: "center",
    padding: tokens.spacing.space[2],
  },
});

export default Login;
