{"expo": {"name": "Eneco", "slug": "eneco", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/eneco-logo.png", "scheme": "eneco-acc.okta-emea.com", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.mafpoc", "infoPlist": {"NSBluetoothAlwaysUsageDescription": "This app uses Bluetooth to connect to audio devices for playback.", "NSMicrophoneUsageDescription": "This app needs access to your microphone for audio playback."}}, "extra": {"eas": {"projectId": "46594136-7504-43e6-8489-45093be0658a"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.mafpoc", "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_CONNECT", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "stylesheets": ["./assets/styles/fonts.css", "./assets/styles/global.css"]}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/eneco-logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"image": "./assets/images/eneco-logo.png", "backgroundColor": "#000000"}}], ["expo-font", {"fonts": ["./assets/fonts/etelkaBlack.ttf", "./assets/fonts/etelkaBold.ttf", "./assets/fonts/etelkaLight.ttf", "./assets/fonts/etelkaMedium.ttf"], "android": {"fonts": [{"fontFamily": "Etelka", "fontDefinitions": [{"path": "./assets/fonts/etelkaLight.ttf", "weight": 200}, {"path": "./assets/fonts/etelkaMedium.ttf", "weight": 500}, {"path": "./assets/fonts/etelkaBold.ttf", "weight": 700}]}]}, "ios": {"fonts": ["./assets/fonts/etelkaLight.ttf", "./assets/fonts/etelkaMedium.ttf", "./assets/fonts/etelkaBold.ttf"]}}], "expo-web-browser", "expo-secure-store", "expo-av", ["react-native-ble-plx", {"isBackgroundEnabled": false, "modes": ["peripheral", "central"], "bluetoothAlwaysPermission": "This app uses Bluetooth to connect to audio devices for playback."}]], "experiments": {"typedRoutes": true}}}