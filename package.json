{"name": "maf-poc", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web --port 8888", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "^2.0.1", "dotenv": "^16.5.0", "expo": "~53.0.9", "expo-auth-session": "^6.1.5", "expo-av": "^15.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-ble-plx": "^3.5.0", "react-native-edge-to-edge": "^1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-nitro-modules": "^0.25.2", "react-native-permissions": "^5.4.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-unistyles": "^3.0.0-rc.4", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "victory": "^37.3.6", "victory-native": "^41.17.1", "expo-dev-client": "~5.1.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.8.3"}, "private": true}