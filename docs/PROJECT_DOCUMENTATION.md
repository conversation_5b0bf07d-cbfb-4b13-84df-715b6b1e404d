# MAF POC - Comprehensive Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Tech Stack](#tech-stack)
3. [Project Structure](#project-structure)
4. [Application Pages](#application-pages)
5. [Design System](#design-system)
6. [Authentication](#authentication)
7. [API Integration](#api-integration)
8. [Features by Page](#features-by-page)
9. [Development Setup](#development-setup)
10. [Build and Deployment](#build-and-deployment)

## Project Overview

MAF POC (Mobile Application Framework - Proof of Concept) is a cross-platform energy management application built with React Native and Expo. The application provides users with energy consumption tracking, billing information, and sustainable energy options.

### Key Features
- **Energy Usage Tracking**: Real-time monitoring of electricity and gas consumption
- **Dashboard**: Comprehensive overview of energy usage and costs
- **Billing Integration**: Access to bills and payment information
- **Sustainable Energy**: Information about sustainable energy products
- **Service Management**: Customer service features and support
- **Bluetooth Audio**: Demo functionality for Bluetooth audio features
- **Authentication**: Secure Okta-based authentication system

## Tech Stack

### Core Technologies
- **React Native**: 0.79.2 - Cross-platform mobile development framework
- **Expo**: ~53.0.9 - Development platform and toolchain
- **TypeScript**: ~5.8.3 - Type-safe JavaScript development
- **React**: 19.0.0 - UI library
- **React DOM**: 19.0.0 - Web rendering support

### Navigation & Routing
- **Expo Router**: ~5.0.6 - File-based routing system
- **React Navigation**: 
  - Bottom Tabs: ^7.3.10
  - Elements: ^2.3.8
  - Native: ^7.1.6

### UI & Styling
- **React Native Unistyles**: ^3.0.0-rc.4 - Unified styling system
- **React Native SVG**: 15.11.2 - SVG support
- **Expo Vector Icons**: ^14.1.0 - Icon library
- **React Native Reanimated**: ~3.17.4 - Animation library
- **React Native Gesture Handler**: ~2.24.0 - Gesture handling

### Data Visualization
- **Victory**: ^37.3.6 - Chart library for web
- **Victory Native**: ^41.17.1 - Chart library for React Native

### Authentication & Security
- **Expo Auth Session**: ^6.1.5 - OAuth authentication
- **Expo Secure Store**: ^14.2.3 - Secure storage
- **React Native Permissions**: ^5.4.0 - Permission management

### Bluetooth & Audio
- **React Native BLE PLX**: ^3.5.0 - Bluetooth Low Energy
- **Expo AV**: ^15.1.4 - Audio/Video playback

### Development Tools
- **ESLint**: ^9.25.0 - Code linting
- **Babel**: ^7.25.2 - JavaScript compiler
- **Metro**: Bundler for React Native

### Platform Support
- **iOS**: Native iOS development
- **Android**: Native Android development  
- **Web**: React Native for Web support

## Project Structure

```
maf-poc/
├── app/                          # Expo Router pages
│   ├── (tabs)/                   # Tab-based navigation
│   │   ├── _layout.tsx          # Tab layout configuration
│   │   ├── index.tsx            # Dashboard page
│   │   ├── energy.tsx           # Energy usage page
│   │   ├── sustainable.tsx      # Sustainable energy page
│   │   ├── service.tsx          # Service page
│   │   ├── bluetooth.tsx        # Bluetooth demo (dev only)
│   │   ├── dc-dashboard.tsx     # Digital Core API demo (dev only)
│   │   └── design-system.tsx    # Design system showcase (dev only)
│   ├── energy-usage/            # Energy usage module
│   │   ├── components/          # Energy-specific components
│   │   ├── hooks/               # Energy data hooks
│   │   ├── types/               # Type definitions
│   │   ├── utils/               # Utility functions
│   │   └── mocks/               # Mock data
│   ├── api/                     # API routes
│   ├── _layout.tsx              # Root layout
│   ├── login.tsx                # Login page
│   ├── callback.tsx             # Auth callback
│   ├── consumption-info.tsx     # Consumption details
│   └── +not-found.tsx           # 404 page
├── src/                         # Source code
│   ├── design-system/           # Design system
│   │   ├── components/          # UI components
│   │   ├── tokens/              # Design tokens
│   │   ├── theme.ts             # Theme configuration
│   │   ├── provider.tsx         # Theme provider
│   │   └── unistyles.ts         # Unistyles configuration
│   ├── auth/                    # Authentication
│   │   ├── AuthContext.tsx      # Auth context provider
│   │   └── OktaService.ts       # Okta integration
│   ├── api/                     # API services
│   │   ├── ApiClient.ts         # HTTP client
│   │   ├── DigitalCoreService.ts # Digital Core API
│   │   └── types.ts             # API types
│   ├── components/              # Shared components
│   ├── services/                # Business logic services
│   ├── hooks/                   # Custom hooks
│   └── config/                  # Configuration
├── assets/                      # Static assets
│   ├── fonts/                   # Font files
│   ├── images/                  # Images and logos
│   ├── icons/                   # SVG icons
│   ├── audio/                   # Audio files
│   └── styles/                  # Global styles
├── components/                  # Legacy components
├── constants/                   # App constants
├── hooks/                       # Legacy hooks
└── docs/                        # Documentation
```

## Application Pages

### 1. Dashboard (index.tsx)
**Route**: `/(tabs)/`
**Purpose**: Main landing page showing energy overview and quick actions

**Features**:
- Energy usage summary (electricity and heat)
- Return payment estimation
- Daily usage cards
- Quick action buttons (Bills, Products, Interim reading, Standby)
- Notification banner
- Eneco branding

### 2. Energy Usage (energy.tsx)
**Route**: `/(tabs)/energy`
**Purpose**: Detailed energy consumption tracking and analytics

**Features**:
- Interactive charts (Victory.js)
- Time period selection (day, week, month, year)
- Electricity and gas usage breakdown
- Cost analysis
- Historical data comparison
- Mobile-optimized table view

### 3. Sustainable Energy (sustainable.tsx)
**Route**: `/(tabs)/sustainable`
**Purpose**: Sustainable energy products and services

**Features**:
- Product grid layout (2x2)
- Category-based organization
- Product images from stock assets
- Sustainable energy options
- Environmental impact information

### 4. Service (service.tsx)
**Route**: `/(tabs)/service`
**Purpose**: Customer service and support

**Features**:
- Customer information display
- Support ticket management
- Payment options
- Data management
- Notification badges (shows "3" notifications)
- Service request forms

### 5. Login (login.tsx)
**Route**: `/login`
**Purpose**: User authentication

**Features**:
- Okta OAuth integration
- PKCE authentication flow
- Secure token storage
- Auto-redirect after login

### 6. Development Pages (Dev Mode Only)

#### Bluetooth Demo (bluetooth.tsx)
**Route**: `/(tabs)/bluetooth`
**Purpose**: Bluetooth audio functionality demonstration

**Features**:
- Bluetooth device scanning
- Audio playback controls
- Connection status display
- BLE device management

#### Digital Core Dashboard (dc-dashboard.tsx)
**Route**: `/(tabs)/dc-dashboard`
**Purpose**: Digital Core API integration testing

**Features**:
- API endpoint testing
- Data visualization
- Response monitoring
- Error handling demonstration

#### Design System Showcase (design-system.tsx)
**Route**: `/(tabs)/design-system`
**Purpose**: Design system component showcase

**Features**:
- Component library display
- Theme switching
- Interactive examples
- Style guide reference

## Design System

### Components Library

#### Core Components
- **Box**: Layout container with spacing and styling props
- **Button**: Primary, secondary, and rebrand button variants
- **Card**: Content containers with elevation and corners
- **Text**: Typography component with variants (Heading, Paragraph, Caption, Label)
- **Badge**: Status and notification indicators
- **Grid**: Responsive grid layout system
- **Stack**: Vertical and horizontal spacing containers

#### Layout Components
- **PageGrid**: Page-level grid system
- **Bleed**: Full-width content containers
- **Hidden**: Responsive visibility control
- **VisuallyHidden**: Screen reader accessible content

#### Interactive Components
- **ButtonLink**: Link-styled buttons
- **TextLink**: Text-based links
- **IconButton**: Icon-only buttons
- **Image**: Optimized image component
- **Skeleton**: Loading state placeholders

### Design Tokens

#### Colors
- **Primary Colors**: Brand colors and accents
- **Secondary Colors**: Supporting color palette
- **Neutral Colors**: Grayscale palette
- **Feedback Colors**: Success, error, warning, info
- **Background Colors**: Surface and container colors

#### Typography
- **Font Family**: Etelka (custom font)
- **Font Weights**: Light (300), Medium (500), Bold (700), Black (900)
- **Font Sizes**: Responsive scale from XS to XXL
- **Line Heights**: Optimized for readability

#### Spacing
- **Scale**: 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 16, 24, 32
- **Usage**: Consistent spacing throughout the application

#### Borders
- **Radii**: Small (s), Medium (m), Large (l), Round
- **Widths**: Thin, Medium, Thick borders

### Theme System
- **Light Theme**: Default light mode styling
- **Dark Theme**: Dark mode support
- **Theme Provider**: Context-based theme management
- **Auto-switching**: System preference detection

## Authentication

### Okta Integration
- **Provider**: Okta OAuth 2.0 with PKCE
- **Flow**: Authorization Code with PKCE
- **Storage**: Expo Secure Store for token persistence
- **Auto-refresh**: Automatic token renewal

### Security Features
- **Secure Storage**: Encrypted token storage
- **Session Management**: Automatic session validation
- **Logout**: Complete session cleanup
- **Error Handling**: Comprehensive error management

## API Integration

### Digital Core Service
- **Base URL**: Configurable API endpoint
- **Authentication**: Bearer token authentication
- **Error Handling**: Centralized error management
- **Type Safety**: TypeScript interfaces for all endpoints

### API Client
- **HTTP Client**: Axios-based client
- **Interceptors**: Request/response interceptors
- **Retry Logic**: Automatic retry for failed requests
- **Timeout Handling**: Configurable request timeouts

## Features by Page

### Dashboard Features
- ✅ Energy usage overview
- ✅ Return payment estimation
- ✅ Daily usage tracking
- ✅ Quick action navigation
- ✅ Notification system
- ✅ Responsive design

### Energy Usage Features
- ✅ Interactive charts (Victory.js)
- ✅ Time period filtering
- ✅ Usage breakdown by type
- ✅ Cost calculations
- ✅ Historical comparisons
- ✅ Mobile table view

### Sustainable Energy Features
- ✅ Product catalog
- ✅ Category organization
- ✅ Image gallery
- ✅ Product details
- ✅ Environmental metrics

### Service Features
- ✅ Customer profile
- ✅ Support tickets
- ✅ Payment management
- ✅ Data preferences
- ✅ Notification badges

### Development Features
- ✅ Bluetooth connectivity
- ✅ Audio playback
- ✅ API testing tools
- ✅ Design system showcase
- ✅ Component library

## Development Setup

### Prerequisites
- Node.js 18+
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation
```bash
npm install
```

### Development Commands
```bash
npm start          # Start Expo development server
npm run ios        # Run on iOS simulator
npm run android    # Run on Android emulator
npm run web        # Run on web browser
npm run lint       # Run ESLint
```

### Environment Configuration
- Create `.env` file with required environment variables
- Configure Okta settings
- Set API endpoints

## Build and Deployment

### Build Commands
```bash
expo build:ios     # Build for iOS
expo build:android # Build for Android
expo build:web     # Build for web
```

### Deployment
- **iOS**: App Store deployment via Expo Application Services (EAS)
- **Android**: Google Play Store deployment via EAS
- **Web**: Static site deployment to hosting platform

### Configuration Files
- `app.json`: Expo configuration
- `eas.json`: EAS Build configuration
- `metro.config.js`: Metro bundler configuration
- `babel.config.js`: Babel transpilation configuration
